#!/usr/bin/env python3
"""
Newsletter Generation Demo.

This script demonstrates how to use the newsletter generation pipeline
to create AI-powered newsletters from natural language requirements.

Requirements:
    - Set EXA_API_KEY environment variable for search functionality
    - Set GOOGLE_API_KEY environment variable for Gemini access via LiteLLM
"""

import asyncio
import os
from datetime import datetime

from google.adk.runners import Runner

from underlines_adk.agents.newsletter import ManagingEditor, create_managing_editor
from underlines_adk.sessions import LoggedSessionService


async def generate_basic_newsletter():
    """Generate a basic newsletter with default settings."""
    print("\n=== Basic Newsletter Generation ===")

    # Create session service with logging
    session_service = LoggedSessionService()

    # Create runner with the pre-configured ManagingEditor
    runner = Runner(
        agent=ManagingEditor,
        app_name="newsletter_demo",
        session_service=session_service,
    )

    # Define the newsletter request
    initial_state = {
        "user_request": """Create a weekly AI coding newsletter covering:
        - New techniques in AI coding from the field
        - Major product launches and updates
        - Company movements
        - Research related to AI coding
        
        Keep it concise but informative, with key takeaways for each story."""
    }

    print(f"Generating newsletter at {datetime.now()}")
    print(f"Request: {initial_state['user_request'][:100]}...")

    # Create session and run the pipeline
    session = await session_service.create_session(
        app_name="newsletter_demo",
        user_id="demo_user",
        session_id=f"demo_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
    )

    # Execute the pipeline
    async for event in runner.arun(
        user_id="demo_user",
        session_id=session.session_id,
        new_message=initial_state["user_request"],
    ):
        # Could process events here if needed
        if event.author and event.content:
            print(f"\n[{event.author}]: Processing...")

    # Get the final newsletter
    final_state = session.state
    newsletter = final_state.get("newsletter", "No newsletter generated")

    print("\n=== Generated Newsletter ===")
    print(newsletter)

    # Also show intermediate results
    print("\n=== Pipeline Artifacts ===")
    stories = final_state.get("stories_array", "No stories found")
    print(f"Discovered stories: {stories}")

    return newsletter


async def generate_custom_newsletter():
    """Generate a newsletter with custom configuration."""
    print("\n\n=== Custom Newsletter Generation ===")

    # Create a custom-configured editor
    custom_editor = create_managing_editor(
        use_json_repair=True,
        wide_search_iterations=4,  # More thorough story discovery
        research_iterations_per_story=4,  # Deeper research
        research_lookback_days=14,  # Two weeks of content
        newsletter_iterations=6,  # More polish
    )

    session_service = LoggedSessionService()
    runner = Runner(
        agent=custom_editor,
        app_name="custom_newsletter_demo",
        session_service=session_service,
    )

    # Industry-specific request
    initial_state = {
        "user_request": """Create a biotech industry newsletter focused specifically on TL1A antibody research and market analysis:
        
        Focus areas:
        - TL1A antibody research and clinical trials
        - Gene therapy breakthroughs
        - FDA approvals and regulatory updates
        - M&A activity and funding rounds
    
        Geographic interest sections: Europe, Australia, Saudi/UAE, China
        
        Target audience: Biotech VCs and investment analysts interested in the TL1A antibody market.
        Tone: Professional but accessible, emphasize investment implications
        Include specific company names, trial data, and market analysis"""
    }

    print(f"Generating specialized newsletter at {datetime.now()}")
    print("Configuration: Extended research, 2-week lookback")

    session = await session_service.create_session(
        app_name="custom_newsletter_demo",
        user_id="investor_user",
        session_id=f"biotech_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
    )

    # Execute the pipeline
    async for event in runner.arun(
        user_id="investor_user",
        session_id=session.session_id,
        new_message=initial_state["user_request"],
    ):
        if event.author:
            print(f"\n[{event.author}]: Working...")

    # Get results
    final_state = session.state
    newsletter = final_state.get("newsletter", "No newsletter generated")

    print("\n=== Biotech Investment Newsletter ===")
    print(newsletter)

    return newsletter


async def main():
    """Run newsletter generation demos."""
    print("Newsletter Generation Pipeline Demo")
    print("=" * 50)

    # Check for required environment variables
    if not os.getenv("EXA_API_KEY"):
        print("WARNING: EXA_API_KEY not set. Search functionality will fail.")
    if not os.getenv("GOOGLE_API_KEY"):
        print("WARNING: GOOGLE_API_KEY not set. LLM calls will fail.")

    try:
        # Run basic newsletter generation
        basic_newsletter = await generate_basic_newsletter()

        # Run custom newsletter generation
        custom_newsletter = await generate_custom_newsletter()

        print("\n\n=== Demo Complete ===")
        print("Generated 2 newsletters successfully!")

    except Exception as e:
        print(f"\nError during newsletter generation: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
