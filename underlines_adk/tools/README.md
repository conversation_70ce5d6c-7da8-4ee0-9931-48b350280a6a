# Tools Module

<!-- purpose-start -->
This module provides external API integrations and utility functions that agents use to perform their tasks. Tools are designed to be reusable, well-documented functions that handle specific external services like web search and language model interactions.
<!-- purpose-end -->

<!-- architecture-start -->
## Architecture

Tools follow a consistent pattern:
- **Pure Functions**: Stateless functions with clear inputs and outputs
- **Error Handling**: Graceful handling of API failures and edge cases
- **Type Safety**: Full type annotations for all parameters and returns
- **Documentation**: Comprehensive docstrings with examples

```python
def tool_function(param: type) -> return_type:
    """
    Clear description of what the tool does.
    
    Args:
        param: Description of parameter
        
    Returns:
        Description of return value
        
    Raises:
        ExceptionType: When this exception occurs
    """
```
<!-- architecture-end -->

<!-- contents-start -->
## Contents

### External API Tools

- **`exa_tools.py`**: Exa API integration for web search
  - `exa_wide_search()`: Broad search with highlights (30 results)
  - `exa_deep_search()`: Focused search with full text (10 results)  
  - `_exa_search()`: Internal utility function (not for direct agent use)

- **`litellm_tools.py`**: Language model integration
  - `llm`: Configured LiteLLM instance for OpenAI GPT-4o-mini
  - Provides abstraction layer for multiple LLM providers

### Tool Categories

**Search Tools**:
- Web search functionality via Exa API
- Different search strategies (wide vs deep)
- Configurable result counts and content types

**Language Model Tools**:
- Multi-provider LLM access via LiteLLM
- Consistent interface across different models
- Easy model switching and configuration
<!-- contents-end -->

## Usage Examples

### Exa Search Tools

```python
from underlines_adk.tools.exa_tools import exa_wide_search, exa_deep_search

# Wide search for comprehensive coverage
results = exa_wide_search(
    query="artificial intelligence breakthroughs",
    lookback_days=7
)

# Deep search for detailed analysis
detailed_results = exa_deep_search(
    query="specific AI research paper",
    lookback_days=30
)

# Results structure
print(results["type"])  # "exa"
print(len(results["results"]))  # Number of results
for result in results["results"]:
    print(result["title"])
    print(result["url"])
    print(result["highlights"])  # For wide search
    print(result["text"])        # For deep search (if available)
```

### Language Model Tools

```python
from underlines_adk.tools.litellm_tools import llm

# The llm instance is pre-configured and ready to use
# Typically used within agents, not directly
```

## Tool Development

### Creating New Tools

1. **Define Tool Function**:
```python
# tools/my_new_tools.py
import os
from typing import Dict, List
from external_api import ExternalAPIClient

def my_tool_function(param1: str, param2: int) -> Dict:
    """
    Description of what this tool does.
    
    Args:
        param1: Description of first parameter
        param2: Description of second parameter
        
    Returns:
        Dictionary containing tool results with consistent structure
        
    Raises:
        ValueError: If parameters are invalid
        ConnectionError: If external API is unavailable
        
    Example:
        >>> result = my_tool_function("test", 5)
        >>> print(result["status"])
        "success"
    """
    try:
        # Validate inputs
        if not param1:
            raise ValueError("param1 cannot be empty")
        if param2 < 1:
            raise ValueError("param2 must be positive")
            
        # Initialize API client
        api_key = os.getenv("MY_API_KEY")
        if not api_key:
            raise ValueError("MY_API_KEY environment variable not set")
            
        client = ExternalAPIClient(api_key=api_key)
        
        # Make API call
        response = client.call_api(param1, param2)
        
        # Return consistent format
        return {
            "status": "success",
            "data": response.data,
            "metadata": {
                "timestamp": response.timestamp,
                "source": "my_external_api"
            }
        }
        
    except Exception as e:
        # Log error and return error response
        import logging
        logging.error(f"Tool function failed: {e}")
        
        return {
            "status": "error",
            "error": str(e),
            "data": None
        }
```

2. **Add Tests**:
```python
# tests/unit/test_my_new_tools.py
from unittest.mock import patch, MagicMock
import pytest
from underlines_adk.tools.my_new_tools import my_tool_function

@patch('underlines_adk.tools.my_new_tools.ExternalAPIClient')
def test_my_tool_function_success(mock_client):
    # Setup mock
    mock_instance = MagicMock()
    mock_client.return_value = mock_instance
    mock_instance.call_api.return_value = MagicMock(
        data={"result": "test"},
        timestamp="2024-01-15T10:00:00Z"
    )
    
    # Test function
    result = my_tool_function("test", 5)
    
    # Assertions
    assert result["status"] == "success"
    assert result["data"]["result"] == "test"
    mock_instance.call_api.assert_called_once_with("test", 5)

def test_my_tool_function_invalid_input():
    with pytest.raises(ValueError):
        my_tool_function("", 5)
```

3. **Integration Tests**:
```python
# tests/integration/test_my_new_tools_integration.py
import os
import pytest
from underlines_adk.tools.my_new_tools import my_tool_function

@pytest.mark.skipif(
    not os.getenv("MY_API_KEY"),
    reason="MY_API_KEY not set; skipping integration test"
)
def test_my_tool_function_real_api():
    result = my_tool_function("real test", 3)
    assert result["status"] == "success"
    assert "data" in result
```

### Tool Design Principles

**Consistent Return Format**:
```python
# Success response
{
    "status": "success",
    "data": {...},
    "metadata": {
        "timestamp": "ISO format",
        "source": "tool_name"
    }
}

# Error response  
{
    "status": "error",
    "error": "Error description",
    "data": None
}
```

**Error Handling**:
```python
def robust_tool(param: str) -> Dict:
    try:
        # Main logic
        result = external_api_call(param)
        return {"status": "success", "data": result}
    except APIRateLimitError:
        # Specific error handling
        return {"status": "rate_limited", "retry_after": 60}
    except APIConnectionError:
        # Network issues
        return {"status": "connection_error", "error": "API unavailable"}
    except Exception as e:
        # Generic fallback
        logging.error(f"Unexpected error in {__name__}: {e}")
        return {"status": "error", "error": str(e)}
```

**Environment Configuration**:
```python
def get_api_key(key_name: str) -> str:
    """Get API key with clear error message if missing."""
    key = os.getenv(key_name)
    if not key:
        raise ValueError(
            f"{key_name} environment variable not set. "
            f"Please set it to use this tool."
        )
    return key
```

<!-- test-refs-start -->
## Testing

### Unit Tests
Located in `../../tests/unit/`:
- Mock all external API calls
- Test error handling and edge cases
- Verify input validation
- Check output format consistency

### Integration Tests
Located in `../../tests/integration/`:
- Test with real external APIs
- Require appropriate API keys
- Verify actual API behavior
- Test rate limiting and error scenarios

### Running Tests
```bash
# All tool tests
pytest tests/ -k tools

# Specific tool tests
pytest tests/unit/test_exa_tools.py
pytest tests/integration/ -k exa

# With environment variables
EXA_API_KEY=your_key pytest tests/integration/
```
<!-- test-refs-end -->

<!-- doc-refs-start -->
## Documentation

- **[Architecture Guide](../../docs/ARCHITECTURE.md)**: How tools fit into the system
- **[Agent Patterns](../../docs/AGENT_PATTERNS.md)**: How agents use tools
- **[Workflows Guide](../../docs/WORKFLOWS.md)**: Development, usage, and maintenance workflows
- **[Main README](../../README.md)**: Project overview and setup
- **API Documentation**: See individual tool docstrings for detailed API information
<!-- doc-refs-end -->

### API Keys and Configuration

Tools require various API keys set as environment variables:

```bash
# Required for Exa tools
export EXA_API_KEY="your_exa_api_key"

# Required for LiteLLM tools (Gemini)
export GOOGLE_API_KEY="your_google_api_key"

# Or use a .env file
echo "EXA_API_KEY=your_key" >> .env
```

<!-- dependent-refs-start -->
## Dependencies

### External Dependencies
- **exa-py**: Exa API client library
- **litellm**: Multi-provider LLM integration
- **Standard library**: os, datetime, typing, logging

### Internal Dependencies
- **Minimal**: Tools are designed to be self-contained
- **Logging**: Uses Python's standard logging module

### Dependents
- **Agents**: All agents depend on tools for functionality
- **Examples**: Example scripts use tools directly or through agents
- **Tests**: Both unit and integration tests depend on tools
<!-- dependent-refs-end -->

<!-- troubleshooting-start -->
## Troubleshooting

### API Key Issues
```bash
# Check if API keys are set
echo $EXA_API_KEY
echo $GOOGLE_API_KEY

# Test API key validity
python -c "from underlines_adk.tools.exa_tools import exa_wide_search; print(exa_wide_search('test', 1))"
```

### Connection Issues
1. **Network Connectivity**: Check internet connection
2. **API Status**: Verify external API service status
3. **Rate Limits**: Check if you've exceeded API rate limits
4. **Firewall**: Ensure outbound HTTPS connections are allowed

### Tool Function Errors

**Import Errors**:
```python
# Check if tools can be imported
try:
    from underlines_adk.tools.exa_tools import exa_wide_search
    print("Import successful")
except ImportError as e:
    print(f"Import failed: {e}")
```

**Runtime Errors**:
```python
# Test tool function directly
from underlines_adk.tools.exa_tools import exa_wide_search

try:
    result = exa_wide_search("test query", 1)
    print("Tool call successful:", result["status"])
except Exception as e:
    print(f"Tool call failed: {e}")
```

### Performance Issues
1. **API Response Times**: Monitor external API latency
2. **Rate Limiting**: Implement backoff strategies
3. **Timeout Configuration**: Adjust timeout values for slow APIs
4. **Caching**: Consider caching results for repeated queries

### Development Issues

**Adding New External APIs**:
1. Install required client library
2. Add to project dependencies in `pyproject.toml`
3. Follow existing patterns for error handling
4. Add comprehensive tests
5. Document required environment variables

**Tool Not Working in Agents**:
1. Verify tool function signature matches agent expectations
2. Check that tool is properly imported in agent module
3. Ensure tool returns expected data format
4. Test tool independently before using in agent
<!-- troubleshooting-end -->
