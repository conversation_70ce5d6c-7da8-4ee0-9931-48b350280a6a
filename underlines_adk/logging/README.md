# Logging Module

<!-- purpose-start -->
This module provides event logging utilities for the Underlines ADK, specifically designed to capture and serialize Google ADK events to JSON Lines files. It enables comprehensive audit trails, debugging, and analysis of agent interactions.
<!-- purpose-end -->

<!-- architecture-start -->
## Architecture

The logging module provides low-level utilities for event serialization and file writing:

```
┌─────────────────────┐
│     ADK Event       │
│                     │
└─────────┬───────────┘
          │
┌─────────▼───────────┐
│ Event Serialization │
│ (_serialize_event)  │
└─────────┬───────────┘
          │
┌─────────▼───────────┐
│   JSON Lines File   │
│ {session_id}.jsonl  │
└─────────────────────┘
```

The module handles the complex task of converting Google ADK Event objects into JSON-serializable dictionaries while preserving all relevant information.
<!-- architecture-end -->

<!-- contents-start -->
## Contents

### Core Components

- **`json_file_logger.py`**: Main logging implementation
  - `log_event_to_json_file()`: Primary function for logging events
  - `_serialize_event()`: Internal event serialization utility
  - `LOG_DIRECTORY`: Configurable log directory constant

### Key Features

- **Complete Event Serialization**: Captures all event data including content, actions, and metadata
- **JSON Lines Format**: One JSON object per line for easy parsing
- **Error Resilience**: Graceful handling of serialization failures
- **UTC Timestamps**: Consistent timezone handling
- **Session Isolation**: Separate log file per session
- **Non-blocking Operations**: Logging doesn't interfere with agent operations
<!-- contents-end -->

## Usage Examples

### Direct Logging

```python
from underlines_adk.logging import log_event_to_json_file
from google.adk.events import Event

# Log an event directly (typically done by LoggedSessionService)
session_id = "session_abc123"
event = some_adk_event  # Google ADK Event object

log_event_to_json_file(session_id, event)

# Creates/appends to: session_logs/session_abc123.jsonl
```

### Configuration

```python
from underlines_adk.logging import json_file_logger

# Change log directory
json_file_logger.LOG_DIRECTORY = "/custom/log/path"

# Now all logs will go to /custom/log/path/{session_id}.jsonl
```

### Reading Log Files

```python
import json
from pathlib import Path

def read_session_events(session_id: str):
    """Read all events for a session"""
    log_file = Path(f"session_logs/{session_id}.jsonl")
    
    events = []
    if log_file.exists():
        with open(log_file, 'r') as f:
            for line in f:
                events.append(json.loads(line))
    
    return events

# Usage
events = read_session_events("session_abc123")
for event in events:
    print(f"Event: {event['event_id']} at {event['timestamp_utc']}")
```

## Event Serialization

### Serialized Event Structure

The `_serialize_event()` function converts ADK Event objects to dictionaries with this structure:

```json
{
  "timestamp_utc": "2024-01-15T10:30:45.123456Z",
  "session_id": "session_abc123",
  "event_id": "event_xyz789",
  "author": "WideSearchAgent",
  "invocation_id": "inv_456",
  "is_final_response": false,
  "turn_complete": false,
  "content": {
    "role": "assistant",
    "parts": [...]
  },
  "actions": {
    "state_delta": null,
    "artifact_delta": null,
    "transfer_to_agent": null,
    "escalate": false,
    "skip_summarization": false
  },
  "error_code": null,
  "error_message": null
}
```

### Content Serialization

Event content is serialized based on the type of parts:

**Text Content**:
```json
{
  "content": {
    "role": "user",
    "parts": [
      {"text": "Search for AI news"}
    ]
  }
}
```

**Function Calls**:
```json
{
  "content": {
    "role": "assistant", 
    "parts": [
      {
        "function_call": {
          "name": "exa_wide_search",
          "args": {
            "query": "AI news",
            "lookback_days": 7
          }
        }
      }
    ]
  }
}
```

**Function Responses**:
```json
{
  "content": {
    "role": "function",
    "parts": [
      {
        "function_response": {
          "name": "exa_wide_search",
          "response": {
            "type": "exa",
            "results": [...]
          }
        }
      }
    ]
  }
}
```

### Error Handling

The serialization process includes comprehensive error handling:

```python
def _serialize_event(event: Event) -> dict[str, Any]:
    """Convert an ADK Event to a serializable dictionary."""
    try:
        # Main serialization logic
        event_data = {...}
        return event_data
    except Exception as e:
        logger.error(f"Failed to serialize event {event.id}: {e}")
        # Return minimal error event
        return {
            "timestamp_utc": datetime.now(UTC).isoformat(),
            "error": "Failed to serialize event",
            "event_id": event.id,
            "exception": str(e),
        }
```

## Log File Management

### File Naming Convention

- **Format**: `{session_id}.jsonl`
- **Location**: `{LOG_DIRECTORY}/{session_id}.jsonl`
- **Default Directory**: `session_logs/`

### File Operations

**Append-Only**: Log files are always appended to, never overwritten
**Automatic Creation**: Log directory and files are created as needed
**Concurrent Safe**: Multiple processes can log to different session files safely

### Log Rotation

Currently not implemented. For production use, consider:

```python
import os
from pathlib import Path
from datetime import datetime, timedelta

def cleanup_old_logs(days_to_keep: int = 30):
    """Remove log files older than specified days"""
    log_dir = Path("session_logs")
    cutoff_date = datetime.now() - timedelta(days=days_to_keep)
    
    for log_file in log_dir.glob("*.jsonl"):
        if log_file.stat().st_mtime < cutoff_date.timestamp():
            log_file.unlink()
            print(f"Removed old log file: {log_file}")

# Run periodically
cleanup_old_logs(30)
```

## Analysis Utilities

### Event Filtering

```python
def filter_events_by_type(events: list, event_type: str):
    """Filter events by author or content type"""
    if event_type == "user_messages":
        return [e for e in events if e['author'] == 'user']
    elif event_type == "agent_responses":
        return [e for e in events if e['author'] != 'user']
    elif event_type == "final_responses":
        return [e for e in events if e.get('is_final_response')]
    elif event_type == "function_calls":
        return [e for e in events if any(
            'function_call' in part 
            for part in e.get('content', {}).get('parts', [])
        )]
    elif event_type == "errors":
        return [e for e in events if e.get('error_code') or e.get('error_message')]
    else:
        return events
```

### Performance Analysis

```python
from datetime import datetime

def analyze_event_timing(events: list):
    """Analyze timing between events"""
    if len(events) < 2:
        return None
    
    timings = []
    for i in range(1, len(events)):
        prev_time = datetime.fromisoformat(events[i-1]['timestamp_utc'].replace('Z', '+00:00'))
        curr_time = datetime.fromisoformat(events[i]['timestamp_utc'].replace('Z', '+00:00'))
        
        delta = (curr_time - prev_time).total_seconds()
        timings.append({
            'from_event': events[i-1]['event_id'],
            'to_event': events[i]['event_id'],
            'duration_seconds': delta
        })
    
    return timings

# Usage
events = read_session_events("session_abc123")
timings = analyze_event_timing(events)
for timing in timings:
    print(f"Event {timing['from_event']} -> {timing['to_event']}: {timing['duration_seconds']:.2f}s")
```

### Content Analysis

```python
def extract_function_calls(events: list):
    """Extract all function calls from events"""
    function_calls = []
    
    for event in events:
        content = event.get('content', {})
        parts = content.get('parts', [])
        
        for part in parts:
            if 'function_call' in part:
                function_calls.append({
                    'event_id': event['event_id'],
                    'timestamp': event['timestamp_utc'],
                    'function_name': part['function_call']['name'],
                    'arguments': part['function_call']['args']
                })
    
    return function_calls

def extract_text_content(events: list):
    """Extract all text content from events"""
    text_content = []
    
    for event in events:
        content = event.get('content', {})
        parts = content.get('parts', [])
        
        for part in parts:
            if 'text' in part:
                text_content.append({
                    'event_id': event['event_id'],
                    'author': event['author'],
                    'timestamp': event['timestamp_utc'],
                    'text': part['text']
                })
    
    return text_content
```

## Advanced Usage

### Custom Serialization

```python
from underlines_adk.logging.json_file_logger import _serialize_event
import json

def custom_event_processor(session_id: str, event):
    """Custom event processing with additional metadata"""
    # Use standard serialization
    event_data = _serialize_event(event)
    
    # Add custom metadata
    event_data['custom_metadata'] = {
        'processing_version': '1.0',
        'environment': 'production',
        'additional_context': get_additional_context()
    }
    
    # Custom file naming
    log_file = f"custom_logs/{session_id}_{datetime.now().strftime('%Y%m%d')}.jsonl"
    
    with open(log_file, 'a') as f:
        json.dump(event_data, f)
        f.write('\n')
```

### Streaming Analysis

```python
import json
from pathlib import Path

def stream_log_analysis(session_id: str):
    """Analyze logs in real-time as they're written"""
    log_file = Path(f"session_logs/{session_id}.jsonl")
    
    # Read existing content
    if log_file.exists():
        with open(log_file, 'r') as f:
            for line in f:
                event = json.loads(line)
                process_event_realtime(event)
    
    # Monitor for new content (simplified example)
    # In practice, use file watching libraries like watchdog
    last_size = log_file.stat().st_size if log_file.exists() else 0
    
    while True:
        if log_file.exists():
            current_size = log_file.stat().st_size
            if current_size > last_size:
                with open(log_file, 'r') as f:
                    f.seek(last_size)
                    for line in f:
                        event = json.loads(line)
                        process_event_realtime(event)
                last_size = current_size
        
        time.sleep(1)  # Check every second

def process_event_realtime(event):
    """Process individual events in real-time"""
    if event.get('error_code'):
        print(f"ERROR: {event['error_message']}")
    elif event.get('is_final_response'):
        print(f"FINAL RESPONSE from {event['author']}")
```

<!-- test-refs-start -->
## Testing

### Unit Tests
Located in `../../tests/unit/`:
- Test event serialization with various event types
- Test error handling and edge cases
- Test file operations and directory creation
- Mock file system operations for isolated testing

### Integration Tests
- Test with real ADK events
- Verify log file creation and content
- Test concurrent logging scenarios

### Running Tests
```bash
# Logging-specific tests
pytest tests/ -k logging

# Test with temporary directories
pytest tests/unit/ -k json_file_logger
```
<!-- test-refs-end -->

<!-- doc-refs-start -->
## Documentation

- **[Logging Guide](../../docs/LOGGING.md)**: Comprehensive logging system documentation
- **[Architecture Guide](../../docs/ARCHITECTURE.md)**: How logging fits into the system
- **[Workflows Guide](../../docs/WORKFLOWS.md)**: Development, usage, and maintenance workflows
- **[Sessions Documentation](../sessions/README.md)**: How sessions use logging
- **[Main README](../../README.md)**: Project overview
<!-- doc-refs-end -->

<!-- dependent-refs-start -->
## Dependencies

### External Dependencies
- **google-adk**: Event objects and types
- **Standard library**: json, logging, datetime, pathlib

### Internal Dependencies
- **Minimal**: Designed to be a low-level utility
- **No internal imports**: Can be used independently

### Dependents
- **Sessions Module**: LoggedSessionService uses this for actual logging
- **Callbacks Module**: Alternative callback-based logging implementation
- **Tests**: Unit and integration tests depend on logging functionality
<!-- dependent-refs-end -->

<!-- troubleshooting-start -->
## Troubleshooting

### Log Files Not Created

**Check Permissions**:
```python
import os
from pathlib import Path

log_dir = Path("session_logs")
print(f"Directory exists: {log_dir.exists()}")
print(f"Directory writable: {os.access(log_dir, os.W_OK)}")

# Create directory if needed
log_dir.mkdir(exist_ok=True)
```

**Check Disk Space**:
```bash
df -h .  # Check available disk space
```

### Serialization Errors

**Debug Event Serialization**:
```python
from underlines_adk.logging.json_file_logger import _serialize_event

try:
    serialized = _serialize_event(problematic_event)
    print("Serialization successful")
except Exception as e:
    print(f"Serialization failed: {e}")
    # Check event attributes
    print(f"Event ID: {problematic_event.id}")
    print(f"Event author: {problematic_event.author}")
```

### Performance Issues

**Large Log Files**:
```python
from pathlib import Path

def check_log_file_sizes():
    """Check sizes of all log files"""
    log_dir = Path("session_logs")
    
    for log_file in log_dir.glob("*.jsonl"):
        size_mb = log_file.stat().st_size / (1024 * 1024)
        print(f"{log_file.name}: {size_mb:.2f} MB")

check_log_file_sizes()
```

**High Volume Logging**:
- Consider log rotation strategies
- Monitor disk I/O performance
- Use SSD storage for better performance
- Implement asynchronous logging for high-throughput scenarios

### JSON Parsing Errors

**Validate Log File Format**:
```python
import json
from pathlib import Path

def validate_log_file(session_id: str):
    """Validate that log file contains valid JSON Lines"""
    log_file = Path(f"session_logs/{session_id}.jsonl")
    
    if not log_file.exists():
        print(f"Log file not found: {log_file}")
        return False
    
    line_number = 0
    try:
        with open(log_file, 'r') as f:
            for line in f:
                line_number += 1
                json.loads(line)  # Will raise exception if invalid JSON
        
        print(f"Log file valid: {line_number} lines")
        return True
        
    except json.JSONDecodeError as e:
        print(f"Invalid JSON at line {line_number}: {e}")
        return False

# Usage
validate_log_file("session_abc123")
```

### Memory Issues

**Large Event Objects**:
- Monitor memory usage during serialization
- Consider truncating very large text content
- Implement event size limits if needed

**File Handle Leaks**:
- Ensure proper file closing (using context managers)
- Monitor open file handles in long-running applications
<!-- troubleshooting-end -->
