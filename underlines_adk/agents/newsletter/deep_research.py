"""
Stage 2: Parallel Deep Research Agents.

This module implements the second stage of the newsletter pipeline, which
conducts parallel deep research on each identified story. It uses a minimal
custom BaseAgent to dynamically create and orchestrate parallel research loops.

Components:
    - FanOutResearchAgent: Minimal custom agent that orchestrates parallel research
    - create_deep_research_loop: Factory for individual story research loops
    - DeepResearcherAgent: Performs in-depth research on a single story
    - DeepResearchCritic: Validates research quality and completeness

The FanOutResearchAgent is the only custom Python code in the pipeline,
handling dynamic parallelization and result aggregation.
"""

import json
from typing import AsyncIterator, List

from google.adk.agents import BaseAgent, LlmAgent, LoopAgent, ParallelAgent
from google.adk.agents.invocation_context import InvocationContext
from google.adk.events import Event, EventActions

from underlines_adk.tools.exa_tools import exa_deep_search


def create_deep_researcher_agent(
    story_topic: str, story_index: int, lookback_days: int = 7
) -> LlmAgent:
    """Create a deep research agent for a specific story.

    Args:
        story_topic: The topic to research
        story_index: Index of the story (for unique naming)
        lookback_days: Number of days to look back for research

    Returns:
        LlmAgent configured for deep research on the story
    """
    return LlmAgent(
        name=f"DeepResearcher_{story_index}",
        model="gemini-2.0-flash-exp",
        instruction=f"""You are a deep research specialist focusing on: "{story_topic}"

Read state.user_request for context about what the user needs.

Conduct thorough research on this specific story using your search tools:
- Find multiple credible sources
- Get specific facts, quotes, and data
- Look for different perspectives
- Verify claims across sources
- Note publication dates and author credibility

Focus on content from the last {lookback_days} days unless historical context is needed.

Write comprehensive research findings to state.current_deep_research_draft_{story_index}.""",
        tools=[exa_deep_search],
        output_key=f"current_deep_research_draft_{story_index}",
    )


def create_deep_research_critic(story_topic: str, story_index: int) -> LlmAgent:
    """Create a research quality critic for a specific story.

    Args:
        story_topic: The topic being researched
        story_index: Index of the story (for unique naming)

    Returns:
        LlmAgent configured to critique research quality
    """
    return LlmAgent(
        name=f"DeepResearchCritic_{story_index}",
        model="gemini-2.0-flash-exp",
        instruction=f"""You are a research quality critic for the story: "{story_topic}"

Review the research in state.current_deep_research_draft_{story_index}.

Evaluate for:
- Depth and comprehensiveness
- Source quality and diversity
- Factual accuracy and verification
- Proper citations and quotes
- Balanced perspectives
- Relevance to the user's requirements

If improvements are needed, provide specific feedback.

If the research is excellent and complete, reply with:
FINAL ANALYSIS STORY {story_index}: [followed by the complete research text]

This will mark the research as final and ready for aggregation.""",
        output_key=f"story_part_{story_index}",
    )


def create_deep_research_loop(
    story_topic: str, story_index: int, max_iterations: int = 3, lookback_days: int = 7
) -> LoopAgent:
    """Create a research loop for a single story.

    Args:
        story_topic: The topic to research
        story_index: Index for unique identification
        max_iterations: Maximum research iterations
        lookback_days: Days to look back for research

    Returns:
        LoopAgent that researches and validates a single story
    """
    researcher = create_deep_researcher_agent(story_topic, story_index, lookback_days)
    critic = create_deep_research_critic(story_topic, story_index)

    return LoopAgent(
        name=f"DeepResearchLoop_{story_index}",
        sub_agents=[researcher, critic],
        max_iterations=max_iterations,
    )


class FanOutResearchAgent(BaseAgent):
    """Minimal custom agent that orchestrates parallel deep research.

    This agent:
    1. Parses the stories_array JSON
    2. Creates parallel research loops for each story
    3. Executes them concurrently via ParallelAgent
    4. Aggregates results into story_bundle

    This is the only custom Python code in the pipeline, keeping the
    implementation minimal and focused on dynamic orchestration.
    """

    def __init__(
        self,
        name: str = "FanOutResearchAgent",
        max_iterations_per_story: int = 3,
        lookback_days: int = 7,
        **kwargs,
    ):
        """Initialize the fan-out research orchestrator.

        Args:
            name: Agent name
            max_iterations_per_story: Max iterations for each story research
            lookback_days: Days to look back for research
            **kwargs: Additional BaseAgent parameters
        """
        super().__init__(name=name, **kwargs)
        self._max_iterations_per_story = max_iterations_per_story
        self._lookback_days = lookback_days

    async def _run_async_impl(
        self, ctx: InvocationContext, **kwargs
    ) -> AsyncIterator[Event]:
        """Execute parallel research on all stories with proper error handling."""
        # MANDATORY: Validate required inputs
        user_request = ctx.session.state.get("user_request", "")
        stories_array_str = ctx.session.state.get("stories_array", "")

        # MANDATORY: Explicit error handling
        if not user_request:
            raise ValueError("No user requirements found for research coordination")
        if not stories_array_str:
            raise ValueError("No stories array found for research coordination")

        # Parse the stories array
        individual_story_topics: List[str] = []
        try:
            parsed_json = json.loads(stories_array_str)
            if isinstance(parsed_json, list) and all(
                isinstance(item, str) for item in parsed_json
            ):
                individual_story_topics = parsed_json
            else:
                raise ValueError(
                    f"stories_array must be a JSON array of strings, got: {type(parsed_json)}"
                )
        except json.JSONDecodeError as e:
            raise ValueError(f"Failed to parse stories_array as JSON: {str(e)}")

        if not individual_story_topics:
            raise ValueError("stories_array is empty - no stories to research")

        # Create research loops for each story
        deep_research_loops = []
        for i, story_topic in enumerate(individual_story_topics):
            loop = create_deep_research_loop(
                story_topic=story_topic,
                story_index=i,
                max_iterations=self._max_iterations_per_story,
                lookback_days=self._lookback_days,
            )
            deep_research_loops.append(loop)

        # Execute all loops in parallel
        parallel_executor = ParallelAgent(
            name="StoryParallelResearchExecutor", sub_agents=deep_research_loops
        )

        # Run the parallel agent
        async for event in parallel_executor.run_async(ctx):
            yield event

        # Collect and aggregate results
        collected_story_parts = []
        for i in range(len(individual_story_topics)):
            part_content = ctx.session.state.get(f"story_part_{i}", "")
            if part_content:
                # Remove the FINAL ANALYSIS prefix
                prefix = f"FINAL ANALYSIS STORY {i}:"
                if part_content.startswith(prefix):
                    part_content = part_content[len(prefix) :].strip()
                collected_story_parts.append(part_content)

        # Join all research into story_bundle
        story_bundle = "\n\n---\n\n".join(collected_story_parts)

        # Update state with the aggregated research
        yield Event(
            author=self.name,
            actions=EventActions(state_delta={"story_bundle": story_bundle}),
        )


# Export components
__all__ = [
    "FanOutResearchAgent",
    "create_deep_researcher_agent",
    "create_deep_research_critic",
    "create_deep_research_loop",
]
