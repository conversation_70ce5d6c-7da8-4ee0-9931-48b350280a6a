# Underlines ADK Package

<!-- purpose-start -->
The main package for Underlines ADK, containing all core components for building intelligent web search agents. This package provides agents, tools, session management, and logging utilities built on Google's Agent Development Kit.
<!-- purpose-end -->

<!-- architecture-start -->
## Architecture

The package is organized into focused modules:

```
underlines_adk/
├── agents/          # AI agents for different search strategies
├── tools/           # External API integrations and utilities  
├── sessions/        # Session management with logging capabilities
├── logging/         # Event logging utilities
└── callbacks/       # Event handling callbacks
```

Each module is designed to be independently usable while working together seamlessly.
<!-- architecture-end -->

<!-- contents-start -->
## Contents

### Core Modules

- **`agents/`**: AI agents that perform specific tasks
  - `wide_search_agent.py`: Broad web search agent using Exa API
  - `deep_search_agent.py`: Placeholder for focused search agent

- **`tools/`**: External service integrations and utilities
  - `exa_tools.py`: Exa API integration for web search
  - `litellm_tools.py`: Language model integration via LiteLLM

- **`sessions/`**: Session management services
  - `logged_session_service.py`: Session service with automatic logging

- **`logging/`**: Event logging infrastructure
  - `json_file_logger.py`: JSON Lines file logging implementation

- **`callbacks/`**: Event handling callbacks
  - `json_logger.py`: Callback-based JSON logging (alternative implementation)

### Package Files

- **`__init__.py`**: Package initialization (currently minimal)
<!-- contents-end -->

## Quick Start

### Basic Agent Usage

```python
from underlines_adk.agents.wide_search_agent import exa_agent
from underlines_adk.sessions import LoggedSessionService
from google.adk.runners import Runner

# Create session service with logging
session_service = LoggedSessionService()

# Create and configure runner
runner = Runner(
    agent=exa_agent,
    app_name="my_search_app",
    session_service=session_service
)

# Use the runner for searches...
```

### Custom Tool Integration

```python
from underlines_adk.tools.exa_tools import exa_wide_search

# Use tools directly
results = exa_wide_search(
    query="artificial intelligence news",
    lookback_days=7
)
```

### Session Logging

```python
from underlines_adk.sessions import LoggedSessionService

# Automatic logging to session_logs/{session_id}.jsonl
session_service = LoggedSessionService()
```

<!-- test-refs-start -->
## Testing

- **Unit Tests**: `../tests/unit/` - Tests for individual components
- **Integration Tests**: `../tests/integration/` - End-to-end testing with real APIs
- **Test Commands**:
  ```bash
  # Run all tests
  pytest
  
  # Run specific module tests
  pytest tests/unit/test_wide_search_agent.py
  pytest tests/integration/test_wide_search_integration.py
  ```
<!-- test-refs-end -->

<!-- doc-refs-start -->
## Documentation

- **[Main README](../README.md)**: Project overview and setup
- **[Architecture Guide](../docs/ARCHITECTURE.md)**: Detailed system architecture
- **[Agent Patterns](../docs/AGENT_PATTERNS.md)**: Guide to developing agents
- **[Logging Guide](../docs/LOGGING.md)**: Session logging and event handling
- **[Workflows Guide](../docs/WORKFLOWS.md)**: Development, usage, and maintenance workflows
- **Module-specific READMEs**: Each subdirectory contains detailed documentation
<!-- doc-refs-end -->

## Development

### Adding New Components

**New Agent**:
1. Create agent module in `agents/`
2. Follow patterns from existing agents
3. Add comprehensive docstrings
4. Include unit tests

**New Tool**:
1. Add tool functions to appropriate module in `tools/`
2. Follow error handling patterns
3. Include type hints and docstrings
4. Add integration tests if external API is involved

**New Service**:
1. Implement in appropriate module
2. Follow async patterns
3. Add comprehensive error handling
4. Include both unit and integration tests

### Code Standards

- **Type Hints**: All functions should have complete type annotations
- **Docstrings**: Follow Google/NumPy docstring format
- **Error Handling**: Graceful degradation with informative messages
- **Testing**: Both unit tests (mocked) and integration tests (real APIs)
- **Async**: Use async/await for all I/O operations

<!-- dependent-refs-start -->
## Dependencies

### External Dependencies
- **google-adk**: Core agent framework
- **exa-py**: Exa API client for web search
- **litellm**: Multi-provider LLM integration

### Internal Dependencies
- Modules are designed to be loosely coupled
- `agents/` depends on `tools/`
- `sessions/` depends on `logging/`
- `callbacks/` provides alternative to direct logging
<!-- dependent-refs-end -->

<!-- troubleshooting-start -->
## Troubleshooting

### Common Issues

**Import Errors**
```python
# Ensure package is properly installed
pip install -e .
# Or with Poetry
poetry install
```

**Missing API Keys**
```bash
# Set required environment variables
export EXA_API_KEY="your_key_here"
```

**Agent Not Responding**
- Check API key configuration
- Verify network connectivity
- Review session logs for errors
- Check tool function implementations

**Logging Issues**
- Ensure `session_logs/` directory is writable
- Check disk space availability
- Review Python logging output for errors

### Debug Mode

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# This will show detailed logging from all components
```

### Performance Issues

- Monitor API rate limits
- Check session log file sizes
- Consider async operation patterns
- Review tool timeout configurations
<!-- troubleshooting-end -->
