# Underlines ADK - AI Newsletter Generation System

An intelligent newsletter generation platform built on Google ADK that transforms natural language requirements into comprehensive, well-researched newsletters.

## What This System Does

Transform natural language descriptions like:

- *"Weekly tech newsletter with AI developments and business implications"*
- *"Daily biotech updates focusing on TL1A research and clinical trials"*
- *"Monthly fintech regulatory updates with competitive analysis"*

Into professional-quality newsletters with proper research, citations, and analysis.

## Key Features

- **Universal Flexibility**: Generate any type of newsletter from natural language descriptions
- **Professional Quality**: Research depth and citation standards that match human experts
- **Existing Agent Integration**: Leverages proven WideSearchAgent and DeepSearchAgent through orchestration
- **ADK-Native Architecture**: Built using Google ADK patterns (SequentialAgent, ParallelAgent, LoopAgent)

## Architecture Overview

TODO

## Core Principles

1. **Orchestration Over Duplication**: Use existing WideSearchAgent and DeepSearchAgent through coordination layers
2. **Universal Flexibility**: Every component works for any newsletter type (daily news, biotech VC, fintech, academic)
3. **Natural Language Requirements**: Work directly with user prose, no structured data extraction
4. **LLM-Driven Quality**: Use tool-based escalation and LLM judgment, not programmatic validation

## Target Users

- **General News Readers**: Personalized news digests and topic summaries
- **Industry Professionals**: Biotech VCs tracking research and companies
- **Business Teams**: Fintech startups needing regulatory and competitive intelligence

## Technology Stack

- **Google ADK**: Agent orchestration and workflow management
- **Exa API**: Web search and content discovery
- **Google Gemini**: LLM for analysis and synthesis
- **Python 3.13+**: Implementation language
- **Poetry**: Dependency management