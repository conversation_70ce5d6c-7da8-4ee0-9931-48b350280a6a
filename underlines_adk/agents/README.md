# Underlines ADK Agents

### Purpose
<!-- readme:purpose-start -->
Collection of intelligent agents built with Google's Agent Development Kit for various information processing tasks.
Each agent package implements a specific workflow using ADK's declarative patterns and minimal custom code.
<!-- readme:purpose-end -->

### Architecture
<!-- readme:architecture-start -->
The agents directory contains specialized agent packages, each implementing a complete workflow using ADK primitives:

- **Workflow Agents**: SequentialAgent, LoopAgent, ParallelAgent for orchestration
- **LLM Agents**: Task-specific agents with tools and prompts
- **Custom Agents**: Minimal BaseAgent implementations for dynamic behavior
- **State Management**: Agents communicate via shared session state
- **Tool Integration**: Agents use Exa search and other tools from underlines_adk.tools

Each package follows ADK best practices:
1. Declarative agent composition
2. final_answer mechanism for loop control
3. Minimal custom Python code
4. Clear state key contracts between agents
<!-- readme:architecture-end -->

### Contents
<!-- readme:contents-start -->
```text
agents/
├── README.md                  # This documentation
└── newsletter/               # Newsletter generation pipeline
    ├── __init__.py
    ├── story_identification.py
    ├── deep_research.py
    ├── newsletter_generation.py
    ├── managing_editor.py
    └── README.md
```
<!-- readme:contents-end -->

### Test Refs
<!-- readme:test-refs-start -->
* tests/unit/test_newsletter_agents.py
* tests/integration/test_newsletter_pipeline.py
<!-- readme:test-refs-end -->

### Doc Refs
<!-- readme:doc-refs-start -->
* underlines_adk/agents/newsletter/README.md - Newsletter pipeline documentation
* docs/prd.md - Product requirements
* docs/tech-spec.md - Technical architecture
<!-- readme:doc-refs-end -->

### Dependent Refs
<!-- readme:dependent-refs-start -->
* examples/newsletter_demo.py - Newsletter generation examples
* underlines_adk/tools/exa_tools.py - Search tools used by agents
<!-- readme:dependent-refs-end -->

### Examples
<!-- readme:examples-start -->
```python
# Using the newsletter generation pipeline
from underlines_adk.agents.newsletter import ManagingEditor
from underlines_adk.sessions import LoggedSessionService
from google.adk.runners import Runner

# Initialize with session service
session_service = LoggedSessionService()

# Create runner with the ManagingEditor
runner = Runner(
    agent=ManagingEditor,
    app_name="newsletter_app",
    session_service=session_service
)

# Generate a newsletter
initial_state = {
    "user_request": "Create a weekly tech newsletter covering AI and robotics"
}

result = await runner.arun(
    user_id="user_123",
    session_id="session_001", 
    initial_state=initial_state
)

newsletter = result.state.get("newsletter")
print(newsletter)
```
<!-- readme:examples-end -->

### Todos
<!-- readme:todos-start -->
* Add more agent packages for different use cases
* Create shared utilities for common agent patterns
* Implement agent composition helpers
<!-- readme:todos-end --> 