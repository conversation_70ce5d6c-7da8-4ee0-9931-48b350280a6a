"""
Stage 3: Newsletter Generation Agents.

This module implements the final stage of the newsletter pipeline, which
crafts and polishes the newsletter from the researched content. It uses
ADK's LoopAgent with iterative refinement until the reviewer is satisfied.

Agents:
    - NewsletterWriterAgent: Drafts the newsletter from research
    - NewsletterReviewerAgent: Reviews and polishes the newsletter

The reviewer uses the FINAL NEWSLETTER: prefix to trigger ADK's final_answer
mechanism, outputting the final version to state.newsletter.
"""

from google.adk.agents import LlmAgent, LoopAgent


def create_newsletter_loop(max_iterations: int = 5) -> LoopAgent:
    """Create the newsletter generation loop.

    This factory ensures new agent instances are created each time, preventing
    parenting issues.

    Args:
        max_iterations: Maximum writing/review cycles (default: 5 for polish)

    Returns:
        LoopAgent that produces the final newsletter in state.newsletter
    """
    newsletter_writer_agent = LlmAgent(
        name="NewsletterWriterAgent",
        model="gemini-2.0-flash-exp",
        instruction="""You are a professional newsletter writer. Your task is to create a compelling newsletter.

Read state.user_request and state.story_bundle.

The user_request contains the specific requirements for the newsletter (style, focus, length, etc.).
The story_bundle contains comprehensive research on all stories to include.

Create a well-structured newsletter that:
- Meets all requirements specified in user_request
- Incorporates all researched stories from story_bundle
- Uses engaging headlines and clear sections
- Maintains consistent tone and style throughout
- Includes proper attribution for all sources
- Provides analysis and context, not just facts
- Concludes with key takeaways or future outlook

Write your draft newsletter to state.current_newsletter_draft.""",
        output_key="current_newsletter_draft",
    )

    newsletter_reviewer_agent = LlmAgent(
        name="NewsletterReviewerAgent",
        model="gemini-2.0-flash-exp",
        instruction="""You are a senior newsletter editor. Your task is to review and perfect the newsletter.

Read:
- state.user_request: Original requirements to ensure alignment
- state.story_bundle: Source research for fact-checking
- state.current_newsletter_draft: The draft to review

Evaluate the newsletter for:
- Alignment with user requirements
- Accuracy and proper attribution
- Engaging writing and flow
- Consistent style and tone
- Completeness of coverage
- Professional presentation
- Grammar and clarity

If improvements are needed, provide specific editorial feedback.

If the newsletter is excellent and ready for publication, respond with:
FINAL NEWSLETTER: [followed by the complete, polished newsletter]

This will mark the newsletter as final and ready for delivery.""",
        output_key="newsletter",
    )

    return LoopAgent(
        name="NewsletterLoop",
        sub_agents=[newsletter_writer_agent, newsletter_reviewer_agent],
        max_iterations=max_iterations,
    )


# Export components
__all__ = [
    "create_newsletter_loop",
]
