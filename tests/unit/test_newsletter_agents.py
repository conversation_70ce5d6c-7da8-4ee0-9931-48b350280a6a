"""
Unit tests for newsletter generation agents.

This module tests the basic functionality of the newsletter pipeline agents,
ensuring they can be imported, instantiated, and follow proper patterns.
"""

from unittest.mock import MagicMock

import pytest
from google.adk.agents import BaseAgent, LoopAgent, SequentialAgent

from underlines_adk.agents.newsletter import (
    FanOutResearchAgent,
    create_deep_research_loop,
    create_deep_researcher_agent,
    create_json_repair_agent,
    create_managing_editor,
    create_newsletter_loop,
    create_wide_search_loop,
)
from underlines_adk.tools.exa_tools import exa_deep_search, exa_wide_search


class TestAgentFactories:
    """Test agent factory functions."""

    def test_create_wide_search_loop(self):
        """Test wide search loop creation."""
        loop = create_wide_search_loop()
        assert isinstance(loop, LoopAgent)
        assert loop.name == "WideSearchLoop"
        assert len(loop.sub_agents) == 2
        assert loop.sub_agents[0].name == "WideSearchAgent"
        assert loop.sub_agents[1].name == "StoryListCritic"

    def test_create_deep_research_loop(self):
        """Test deep research loop creation."""
        loop = create_deep_research_loop("AI safety", 0)
        assert isinstance(loop, LoopAgent)
        assert loop.name == "DeepResearchLoop_0"
        assert len(loop.sub_agents) == 2
        assert loop.sub_agents[0].name == "DeepResearcher_0"
        assert loop.sub_agents[1].name == "DeepResearchCritic_0"

    def test_create_newsletter_loop(self):
        """Test newsletter loop creation."""
        loop = create_newsletter_loop()
        assert isinstance(loop, LoopAgent)
        assert loop.name == "NewsletterLoop"
        assert len(loop.sub_agents) == 2
        assert loop.sub_agents[0].name == "NewsletterWriterAgent"
        assert loop.sub_agents[1].name == "NewsletterReviewerAgent"

    def test_create_managing_editor(self):
        """Test managing editor creation with different configurations."""
        # Default configuration
        editor = create_managing_editor()
        assert isinstance(editor, SequentialAgent)
        # wide search, fan out, newsletter
        assert len(editor.sub_agents) == 3

        # With JSON repair
        editor_with_repair = create_managing_editor(use_json_repair=True)
        # wide search, json repair, fan out, newsletter
        assert len(editor_with_repair.sub_agents) == 4


class TestAgentConfiguration:
    """Test agent configuration and properties."""

    def test_output_keys(self):
        """Test that agents write to correct state keys."""
        # Story identification
        wide_search_loop = create_wide_search_loop()
        wide_search_agent = wide_search_loop.sub_agents[0]
        story_list_critic = wide_search_loop.sub_agents[1]
        assert wide_search_agent.output_key == "current_story_list_text_draft"
        assert story_list_critic.output_key == "stories_array"

        json_repair_agent = create_json_repair_agent()
        assert json_repair_agent.output_key == "stories_array"

        # Newsletter generation
        newsletter_loop = create_newsletter_loop()
        newsletter_writer = newsletter_loop.sub_agents[0]
        newsletter_reviewer = newsletter_loop.sub_agents[1]
        assert newsletter_writer.output_key == "current_newsletter_draft"
        assert newsletter_reviewer.output_key == "newsletter"

    def test_fan_out_configuration(self):
        """Test FanOutResearchAgent configuration options."""
        default_fan_out = FanOutResearchAgent()
        assert default_fan_out._max_iterations_per_story == 3
        assert default_fan_out._lookback_days == 7

        custom_fan_out = FanOutResearchAgent(
            max_iterations_per_story=5, lookback_days=14
        )
        assert custom_fan_out._max_iterations_per_story == 5
        assert custom_fan_out._lookback_days == 14

    def test_agent_tools(self):
        """Test that agents have appropriate tools configured."""
        # WideSearchAgent should have exa_wide_search
        wide_search_agent = create_wide_search_loop().sub_agents[0]
        assert len(wide_search_agent.tools) > 0
        assert wide_search_agent.tools[0] is exa_wide_search

        # Research agents should have exa_deep_search
        researcher = create_deep_researcher_agent("test", 0)
        assert len(researcher.tools) > 0
        assert researcher.tools[0] is exa_deep_search


@pytest.mark.asyncio
class TestErrorHandling:
    """Test proper error handling patterns."""

    async def test_fan_out_missing_user_request(self):
        """Test FanOutResearchAgent raises error for missing user_request."""
        from google.adk.agents.invocation_context import InvocationContext

        from underlines_adk.sessions import LoggedSessionService

        from underlines_adk.sessions import LoggedSessionService

        # Create context without user_request
        session_service = LoggedSessionService()
        session = await session_service.create_session(
            app_name="test", user_id="test_user"
        )
        agent_mock = MagicMock(spec=BaseAgent)
        agent_mock.name = "TestFanOutAgent"
        ctx = InvocationContext(
            session=session,
            session_service=session_service,
            invocation_id="test_inv_1",
            agent=agent_mock,
        )

        fan_out = FanOutResearchAgent()

        # Should raise ValueError for missing user_request
        with pytest.raises(ValueError) as exc_info:
            async for _ in fan_out._run_async_impl(ctx):
                pass

        assert "No user requirements found" in str(exc_info.value)

    async def test_fan_out_missing_stories_array(self):
        """Test FanOutResearchAgent raises error for missing stories_array."""
        from google.adk.agents.invocation_context import InvocationContext

        from underlines_adk.sessions import LoggedSessionService

        # Create context with user_request but no stories_array
        session_service = LoggedSessionService()
        session = await session_service.create_session(
            app_name="test", user_id="test_user"
        )
        session.state["user_request"] = "test requirements"
        agent_mock = MagicMock(spec=BaseAgent)
        agent_mock.name = "TestFanOutAgent"
        ctx = InvocationContext(
            session=session,
            session_service=session_service,
            invocation_id="test_inv_2",
            agent=agent_mock,
        )

        fan_out = FanOutResearchAgent()

        # Should raise ValueError for missing stories_array
        with pytest.raises(ValueError) as exc_info:
            async for _ in fan_out._run_async_impl(ctx):
                pass

        assert "No stories array found" in str(exc_info.value)

    async def test_fan_out_invalid_json(self):
        """Test FanOutResearchAgent raises error for invalid JSON."""
        from google.adk.agents.invocation_context import InvocationContext

        from underlines_adk.sessions import LoggedSessionService

        # Create context with invalid JSON
        session_service = LoggedSessionService()
        session = await session_service.create_session(
            app_name="test", user_id="test_user"
        )
        session.state["user_request"] = "test requirements"
        session.state["stories_array"] = "not valid json"
        agent_mock = MagicMock(spec=BaseAgent)
        agent_mock.name = "TestFanOutAgent"
        ctx = InvocationContext(
            session=session,
            session_service=session_service,
            invocation_id="test_inv_3",
            agent=agent_mock,
        )

        fan_out = FanOutResearchAgent()

        # Should raise ValueError for invalid JSON
        with pytest.raises(ValueError) as exc_info:
            async for _ in fan_out._run_async_impl(ctx):
                pass

        assert "Failed to parse stories_array as JSON" in str(exc_info.value)
