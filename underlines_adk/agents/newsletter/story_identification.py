"""
Stage 1: Story Identification Agents.

This module implements the first stage of the newsletter pipeline, which
identifies relevant stories through wide search and validates them as a
JSON array. The stage uses ADK's LoopAgent with the final_answer mechanism
for automatic termination.

Agents:
    - WideSearchAgent: Performs broad searches to discover story ideas
    - StoryListCritic: Validates and outputs a JSON array of stories
    - JsonRepairAgent: Optional agent to ensure valid JSON output

The critic uses the FINAL STORY ARRAY: prefix to trigger ADK's final_answer
mechanism, outputting a JSON array directly to state.stories_array.
"""

from google.adk.agents import LlmAgent, LoopAgent

from underlines_adk.tools.exa_tools import exa_wide_search


def create_wide_search_loop(max_iterations: int = 3) -> LoopAgent:
    """Create the story identification loop.

    This factory function creates new instances of the agents each time it's
    called, preventing parenting issues in testing or complex pipelines.

    Args:
        max_iterations: Maximum loop iterations before termination.

    Returns:
        LoopAgent configured for story discovery and validation.
    """
    wide_search_agent = LlmAgent(
        name="WideSearchAgent",
        model="gemini-2.0-flash-exp",
        instruction="""You are a news discovery agent. Your task is to find relevant and recent stories based on the user's request.

Read state.user_request carefully.

Use your search tools to find diverse, relevant, and recent stories. Cast a wide net to discover:
- Breaking news and recent developments
- Different perspectives and sources
- Related topics and themes
- Various geographic regions if applicable

Produce a comprehensive bulleted list of story ideas with brief descriptions.

Write your findings to state.current_story_list_text_draft.""",
        tools=[exa_wide_search],
        output_key="current_story_list_text_draft",
    )

    story_list_critic = LlmAgent(
        name="StoryListCritic",
        model="gemini-2.0-flash-exp",
        instruction="""You are a story selection critic. Your task is to review and refine the discovered stories.

Read state.user_request and state.current_story_list_text_draft.

Evaluate the story list for:
- Relevance to the user's request
- Recency and timeliness
- Coverage of different aspects/angles
- Absence of duplicates
- Newsworthiness and quality

If the list needs improvement, provide specific feedback for the WideSearchAgent.

If the list is comprehensive and meets all criteria, output your response in the following format:
FINAL STORY ARRAY: ["story 1 topic", "story 2 topic", "story 3 topic", ...]

The array must be valid JSON with each story as a string describing the topic/angle to research.""",
        output_key="stories_array",
    )

    return LoopAgent(
        name="WideSearchLoop",
        sub_agents=[wide_search_agent, story_list_critic],
        max_iterations=max_iterations,
    )


def create_json_repair_agent() -> LlmAgent:
    """Create the JSON repair agent.

    This factory ensures a new instance is created each time, preventing
    parenting issues.

    Returns:
        LlmAgent configured to repair JSON strings.
    """
    return LlmAgent(
        name="JsonRepairAgent",
        model="gemini-2.0-flash-exp",
        instruction="""You are a JSON repair specialist. Your only task is to ensure valid JSON output.

Read state.stories_array.

If it is not a valid JSON array of strings, repair it. 
If it is already valid, output it unchanged.

Reply ONLY with the valid JSON array string, nothing else.

Example valid output: ["AI regulation in EU", "Climate tech funding surge", "Quantum computing breakthrough"]""",
        output_key="stories_array",
    )


__all__ = [
    "create_wide_search_loop",
    "create_json_repair_agent",
]
