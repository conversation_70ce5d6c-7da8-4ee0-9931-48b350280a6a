## 1. Guiding Philosophy: Ultimate Agentic Purity via Declarative ADK Primitives

This architecture represents the zenith of simplicity and elegance by constructing the entire newsletter generation pipeline from declarative Google Agent Development Kit (ADK) workflow agents (`SequentialAgent`, `LoopAgent`, `ParallelAgent`). Control flow is governed by ADK's `final_answer` mechanism, and imperative Python orchestration is confined to a single, minimal `BaseAgent` for dynamic fan-out and subsequent aggregation. This design prioritizes agentic reasoning and minimizes custom code, allowing ADK's inherent capabilities to manage the workflow.

- **final_answer as the Universal Loop Terminator:** Critic/reviewer agents within `LoopAgents` utilize `auto_mark_final=True`. When their output begins with a predefined prefix (e.g., `FINAL STORY ARRAY:`), ADK automatically sets `final_answer=True`, concluding the loop. The critic's full response, which contains the structured data (e.g., a JSON array) or final content, becomes the loop's definitive output.
- **Ultra-Lean State Surface:** Only three primary ADK state keys are used for handoffs between major stages: `stories_array` (a JSON array of story topic strings), `story_bundle` (aggregated research text), and `newsletter`. The initial `user_request` is the fourth key, initiating the process. Agents read from these, and the designated final agent in each stage writes its conclusive output directly to the subsequent key.
- **Declarative Orchestration:** A top-level `SequentialAgent` (ManagingEditor) executes the main stages. Its definition is a pure list of ADK agent instances.
- **Agentic Data Transformation & Direct Aggregation:**
  - The `StoryListCritic` is prompted to output a JSON array directly for `stories_array`. An optional `JsonRepairAgent` can be added for robustness if LLM output for JSON is inconsistent.
  - Individual `DeepResearchCritic_i` agents in the parallel stage write their final research to unique temporary state keys (e.g., `state.story_part_i`). The `FanOutResearchAgent` then collects these parts *and directly joins them into `state.story_bundle`*, eliminating a separate aggregation agent and state key.
- **Minimal Custom Code for Dynamic Parallelism & Aggregation:** A single, lean custom `FanOutResearchAgent` (`BaseAgent`) handles the dynamic creation and execution of parallel research loops, followed by the collection and joining of their outputs. Its Python code is minimal (target <<20 lines for the core orchestration logic).
- **Focused Configurability:** Control is primarily via `max_iterations` on `LoopAgents` (relying on ADK defaults like 3 where sensible, or specifying higher values for tasks like final newsletter polishing) and standard `LlmAgent` configurations (model selection via LiteLLM, token limits, temperature, etc.).

## 2. The Purely Declarative Pipeline: Three Stages, Four Core Keys

The ManagingEditor is a `SequentialAgent` orchestrating these stages.

**Core State Keys:**

1. `user_request`: (Input) Raw user query.
2. `stories_array`: (Output of Stage 1) A (potentially validated) JSON array of story topic strings.
3. `story_bundle`: (Output of Stage 2) Aggregated research text for all stories.
4. `newsletter`: (Output of Stage 3) The final newsletter.

---

### Process Flow Diagram

```mermaid
graph TD
    subgraph "Stage 1: Story Identification"
        A[WideSearchAgent] --> B[StoryListCritic]
        B --> |"Loop"| A
        B --> |"Final Answer"| C[stories_array]
    end
    
    subgraph "Stage 2: Parallel Deep Research"
        C --> D[FanOutResearchAgent]
        D --> E1[DeepResearchLoop_1]
        D --> E2[DeepResearchLoop_2]
        D --> E3[DeepResearchLoop_N]
        E1 --> F[story_bundle]
        E2 --> F
        E3 --> F
    end
    
    subgraph "Stage 3: Newsletter Generation"
        F --> G[NewsletterWriterAgent]
        G --> H[NewsletterReviewerAgent]
        H --> |"Loop"| G
        H --> |"Final Answer"| I[newsletter]
    end
    
    J[user_request] --> A
    J --> D
    J --> G
```

### Stage 1: Story Identification & JSON Array Output

1. **WideSearchLoop (`LoopAgent`)**
   - **Purpose:** Identify a preliminary list of relevant stories and output them as a JSON array string.
   - **Generator:** `WideSearchAgent` (`LlmAgent`)
     - Reads: `state.user_request`.
     - Task: Runs broad searches (e.g., using a GoogleSearchTool), produces a textual list of potential stories.
     - Writes: `state.current_story_list_text_draft` (intermediate state key, internal to the loop).
   - **Critic:** `StoryListCritic` (`LlmAgent`)
     - Reads: `state.user_request`, `state.current_story_list_text_draft`.
     - Task: Checks coverage, recency, duplication. If good, replies with `FINAL STORY ARRAY:` followed by the refined list formatted *as a JSON array string*. The prompt must strictly enforce this JSON output. (ADK's `output_format="json"` on the agent can be used if available and effective, otherwise strong prompting is key).
     - `auto_mark_final=True` (ADK sets `final_answer=True` if the output starts with the prefix).
     - `output_key="stories_array"` (Writes the JSON array string directly to the main state).
   - `max_iterations`: ADK default (e.g., 3) or specified.
2. **JsonRepairAgent (`LlmAgent`) (Optional Stage)**
   - **Purpose:** Ensure the story list is valid JSON, if the `StoryListCritic` proves unreliable in consistently producing perfect JSON.
   - **Input:** Reads `state.stories_array` (which might be imperfect JSON from the previous step).
   - **Task:** Validates and, if necessary, repairs the input string to be a valid JSON array of strings. Prompted to output *only* the valid JSON array.
   - `output_format="json"` (or strict prompting).
   - **Output:** Overwrites `state.stories_array` with the validated JSON array string.
   - This is a single-turn agent (no loop).

---

### Stage 2: Parallel Deep Research & Direct Bundle Aggregation

- **FanOutResearchAgent (Custom `BaseAgent` - Minimal Python)**
  - **Input:** Reads `state.stories_array` (a JSON array string), `state.user_request`.
  - **Task:**
    1. Parses `state.stories_array` (e.g., using `json.loads()`) into a Python list of topic strings.
    2. For each `story_topic` and its index (i) in the parsed list:
       - Instantiates a `DeepResearchLoop_i` (`LoopAgent`).
         - **Generator:** `DeepResearcherAgent_i` (`LlmAgent`)
           - Input: `story_topic` (passed via constructor/config to the agent instance), `state.user_request`.
           - Task: Deep research on the specific `story_topic` using appropriate tools (e.g., web search with lookback_period).
           - Writes: `state.current_deep_research_draft_[i]` (intermediate).
         - **Critic:** `DeepResearchCritic_i` (`LlmAgent`)
           - Input: `state.current_deep_research_draft_[i]`, `story_topic`.
           - Task: Validates depth, accuracy, citations. If good, replies `FINAL ANALYSIS STORY {i}:` followed by the research text.
           - `auto_mark_final=True`.
           - `output_key=f"story_part_{i}"` (Writes its final research to a unique temporary state key).
         - `max_iterations`: ADK default or specified (e.g., `N_deep_research`). Consider if a single, self-correcting agent per story (generator that revises until perfect then prefixes `FINAL...`) is preferred over a generator/critic pair to reduce LLM calls, based on cost vs. clarity.
    3. Executes all instantiated `DeepResearchLoop_i` instances concurrently using an internally managed `ParallelAgent`.
    4. After the `ParallelAgent` completes, this `FanOutResearchAgent` collects all `state.story_part_i` values.
    5. It then joins these collected parts into a single string and writes it directly: `session.state["story_bundle"] = "\n\n---\n\n".join(collected_story_parts)`.
  - **Output:** No single output key for the agent itself; its primary side effect is populating `state.story_bundle`.

---

### Stage 3: Newsletter Generation

- **NewsletterLoop (`LoopAgent`)**
  - **Purpose:** Draft and review the final newsletter.
  - **Generator:** `NewsletterWriterAgent` (`LlmAgent`)
    - Reads: `state.user_request`, `state.story_bundle`.
    - Task: Drafts the newsletter according to the user's requirements and the researched content.
    - Writes: `state.current_newsletter_draft` (intermediate).
  - **Critic:** `NewsletterReviewerAgent` (`LlmAgent`)
    - Reads: `state.user_request`, `state.story_bundle`, `state.current_newsletter_draft`.
    - Task: Reviews for accuracy, style, tone, completeness. If good, replies `FINAL NEWSLETTER:` followed by the complete newsletter.
    - `auto_mark_final=True`.
    - `output_key="newsletter"` (Writes the final newsletter directly to the main state).
  - `max_iterations`: Specified (e.g., `M_newsletter`, potentially higher than default if more polish iterations are expected).

---

## 3. The ManagingEditor (Top-Level Orchestrator)

The ManagingEditor is a `SequentialAgent` whose `sub_agents` list defines the entire pipeline declaratively.

**Python**

```python
# Conceptual Python definition of the ManagingEditor and its components
# (Actual ADK definition might be more declarative if using a config file format,
# or this Python code would be used to construct the agent graph)

from google.adk.agents import SequentialAgent, LlmAgent, LoopAgent, BaseAgent, ParallelAgent
from google.adk.agents.invocation_context import InvocationContext  # Corrected import for Session/InvocationContext
from google.adk.events import Event, EventActions
from typing import AsyncIterator, List  # For type hinting
import json

# --- Define Individual LlmAgents (Generators and Critics) ---
# (Assuming model, tools, generate_content_config are defined elsewhere or passed in)

# Stage 1 Agents
wide_search_agent = LlmAgent(
    name="WideSearchAgent",
    system_prompt="Read state.user_request. Run broad web searches. Produce a bulleted list of relevant story ideas. Write this list to state.current_story_list_text_draft.",
    # tools=, # Example
    output_key="current_story_list_text_draft"
)
story_list_critic = LlmAgent(
    name="StoryListCritic",
    system_prompt="Read state.user_request and state.current_story_list_text_draft. Check for coverage, recency, and duplication. If the list is good and complete, reply with 'FINAL STORY ARRAY:' followed by the refined list as a JSON array string (e.g., 'FINAL STORY ARRAY:'). Otherwise, provide concrete feedback for WideSearchAgent to improve the list.",
    auto_mark_final=True,
    output_key="stories_array" # Directly writes the JSON string
)
wide_search_loop = LoopAgent(
    name="WideSearchLoop",
    sub_agents=[wide_search_agent, story_list_critic],
    max_iterations=3 # Example, ADK default is often 3
)

# Optional JsonRepairAgent (if needed for robustness)
json_repair_agent = LlmAgent(
    name="JsonRepairAgent",
    system_prompt="Read state.stories_array. If it is not a valid JSON array of strings, repair it. Reply ONLY with the valid JSON array string. If it is already valid, reply with the original content.",
    # output_format="json", # If ADK supports this for LlmAgent and it's reliable
    output_key="stories_array" # Overwrites with repaired (or original) JSON
)

# Stage 2: FanOutResearchAgent (Custom BaseAgent)
class FanOutResearchAgent(BaseAgent):
    def __init__(self, name="FanOutResearchAgent", max_iterations_per_story=3, **kwargs):
        super().__init__(name=name, **kwargs)
        self.max_iterations_per_story = max_iterations_per_story
        # Tool instances (e.g., search tool) would be configured and passed to DeepResearcherAgent_i instances

    async def _run_async_impl(self, session: InvocationContext, **kwargs) -> AsyncIterator[Event]:
        user_request = session.state.get("user_request", "")
        stories_array_str = session.state.get("stories_array", "")
        
        individual_story_topics: List[str] = []
        try:
            parsed_json = json.loads(stories_array_str)
            if isinstance(parsed_json, list) and all(isinstance(item, str) for item in parsed_json):
                individual_story_topics = parsed_json
            else:
                # Attempt to re-parse with an LLM if primary parsing fails or is not a list of strings
                # This is a fallback, strong prompting for StoryListCritic is preferred.
                # For this example, we'll proceed with an empty list if parsing is problematic.
                print(f"Warning: stories_array was not a valid list of strings: {stories_array_str}")
        except json.JSONDecodeError:
            print(f"Error: Failed to decode stories_array JSON: {stories_array_str}")
            # Potentially invoke an LLM to attempt to parse/fix stories_array_str here if critical
            # For now, we'll proceed with an empty list, leading to an empty story_bundle.

        if not individual_story_topics:
            session.state["story_bundle"] = "No stories were identified or parsed from stories_array for deep research."
            yield Event(action=EventActions.UPDATE_STATE, data={"story_bundle": session.state["story_bundle"]})
            return

        deep_research_loops = []
        for i, story_topic in enumerate(individual_story_topics):
            # Define tools for the researcher agent, potentially configured with lookback_period
            # research_tool = SomeSearchTool(lookback_period=session.config.get("research_lookback_period"))
            researcher = LlmAgent(
                name=f"DeepResearcher_{i}",
                system_prompt=f"You are a deep research agent. Your current task is to research the story: '{story_topic}'. Use your search tool. The overall user request for context is: '{user_request}'. Write your findings to state.current_deep_research_draft_{i}.",
                # tools=[research_tool], # Example tool
                output_key=f"current_deep_research_draft_{i}"
            )
            critic = LlmAgent(
                name=f"DeepResearchCritic_{i}",
                system_prompt=f"Review the research in state.current_deep_research_draft_{i} for the story '{story_topic}'. Check for depth, accuracy, and citations. If excellent, reply 'FINAL ANALYSIS STORY {i}:' followed by the complete research text. Otherwise, provide specific feedback.",
                auto_mark_final=True,
                output_key=f"story_part_{i}" # Each critic writes to a unique key
            )
            story_loop = LoopAgent(
                name=f"DeepResearchLoop_{i}",
                sub_agents=[researcher, critic],
                max_iterations=self.max_iterations_per_story
            )
            deep_research_loops.append(story_loop)
        
        if not deep_research_loops:
            session.state["story_bundle"] = "No research loops were created (this should not happen if stories were parsed)."
            yield Event(action=EventActions.UPDATE_STATE, data={"story_bundle": session.state["story_bundle"]})
            return

        # Define the ParallelAgent to run all the story_loops concurrently
        parallel_research_executor = ParallelAgent(
            name="StoryParallelResearchExecutor",
            sub_agents=deep_research_loops
        )

        # Yield events from the parallel executor as it runs
        async for event in parallel_research_executor.arun(session=session):
            yield event

        # After all parallel loops complete, collect their outputs and join them
        collected_story_parts = []
        for i in range(len(individual_story_topics)):
            # The output from each DeepResearchCritic_i is in state[f"story_part_{i}"]
            # ADK's LoopAgent, when its final sub-agent (the critic) has final_answer=True,
            # will have its output (the critic's output) available.
            # If ParallelAgent makes these directly accessible or if they are reliably in session.state:
            part_content = session.state.get(f"story_part_{i}", "")
            if part_content:
                # Remove the "FINAL ANALYSIS STORY i:" prefix before appending
                prefix_to_remove = f"FINAL ANALYSIS STORY {i}:" # Note: index `i` is 0-based
                if part_content.startswith(prefix_to_remove):
                    part_content = part_content[len(prefix_to_remove):].strip()
                collected_story_parts.append(part_content)
        
        session.state["story_bundle"] = "\n\n---\n\n".join(collected_story_parts)
        yield Event(action=EventActions.UPDATE_STATE, data={"story_bundle": session.state["story_bundle"]})

fan_out_research_agent = FanOutResearchAgent() # Instantiate the custom agent

# Stage 3 Agents
newsletter_writer_agent = LlmAgent(
    name="NewsletterWriterAgent",
    system_prompt="Read state.user_request and state.story_bundle. Draft a compelling newsletter based on this information. Write the draft to state.current_newsletter_draft.",
    output_key="current_newsletter_draft"
)
newsletter_reviewer_agent = LlmAgent(
    name="NewsletterReviewerAgent",
    system_prompt="Read state.user_request, state.story_bundle, and state.current_newsletter_draft. Review for accuracy, style, tone, and completeness. If excellent, reply 'FINAL NEWSLETTER:' followed by the complete newsletter. Otherwise, provide revision notes.",
    auto_mark_final=True,
    output_key="newsletter"
)
newsletter_loop = LoopAgent(
    name="NewsletterLoop",
    sub_agents=[newsletter_writer_agent, newsletter_reviewer_agent],
    max_iterations=5 # Example, might need more iterations for polish
)

# Define the ManagingEditor SequentialAgent
# The optional JsonRepairAgent can be added here if testing shows it's necessary.
sub_agent_list_for_managing_editor = [
    wide_search_loop,
    # json_repair_agent, # Uncomment if using
    fan_out_research_agent,
    newsletter_loop
]

ManagingEditor = SequentialAgent(
    name="ManagingEditorPipeline",
    sub_agents=sub_agent_list_for_managing_editor
)

# --- To run this (conceptual, assuming an AdkApp setup) ---
# from vertexai.preview.reasoning_engines import AdkApp
# app = AdkApp(agent=ManagingEditor)
#
# async def run_pipeline():
#     initial_session_state = {"user_request": "Tech news on AI and climate change policy."}
#     # For local testing with AdkApp, you might use app.agent_runner
#     # result_context = await app.agent_runner.run_agent(ManagingEditor, initial_session_state)
#     # For direct agent running if AdkApp is not the entry point:
#     final_context = InvocationContext(state=initial_session_state)
#     async for _ in ManagingEditor.arun(session=final_context):
#         pass # Consume events
#     newsletter_output = final_context.state.get("newsletter")
#     print("--- FINAL NEWSLETTER ---")
#     print(newsletter_output)
#
# if __name__ == "__main__":
#     import asyncio
#     asyncio.run(run_pipeline())
```

## 4. Peak Purity and Practical Robustness

This architecture achieves the "North Star" with practical safeguards:

- **Native Loop Control:** `final_answer` via `auto_mark_final=True` is standard for all loops, simplifying termination logic.
- **Agent-Driven Data Formatting (with Optional Safeguard):** The `StoryListCritic` is prompted to output JSON directly. The `JsonRepairAgent` is an optional, single-turn addition for robustness if the critic's JSON output isn't consistently perfect.
- **Safe Parallel Aggregation:** The `FanOutResearchAgent` orchestrates parallel research. Each `DeepResearchCritic_i` writes its final output to a unique `story_part_i` state key. The `FanOutResearchAgent` then collects these distinct parts *after* parallel execution completes and joins them into `state.story_bundle`. This approach is inherently race-condition-free.
- **Minimal Custom Code:** The `FanOutResearchAgent` is the *only* component with imperative Python. Its role is strictly limited to:
  1. Parsing the `stories_array`.
  2. Dynamically instantiating `DeepResearchLoop_i` sub-agents.
  3. Invoking these loops via an internal `ParallelAgent`.
  4. Collecting the uniquely keyed results from the state and joining them into `story_bundle`. This represents the irreducible minimum of procedural logic required for this dynamic fan-out-and-gather pattern, keeping the custom code footprint exceptionally small.
- **Declarative Core:** The overall pipeline (ManagingEditor) is a clear, declarative sequence of ADK's powerful workflow agents, promoting maintainability and readability.
- **Deployment Path:** This ADK-based system is designed with a clear path from local development (using, for example, `InMemorySessionService`) to scalable cloud deployment on platforms like Vertex AI Agent Engine (using `VertexAiSessionService`).

This design is exceptionally lean, robust for real ADK runs, and maximizes the framework's inherent capabilities while minimizing custom code. It directly reflects the "ultra-lean state surface" of three primary handoff keys (`stories_array`, `story_bundle`, `newsletter`) plus the initial `user_request`.

## Works cited

1. [Multi-Agent Systems in ADK - Google, accessed June 9, 2025](https://google.github.io/adk-docs/agents/multi-agents/)
2. [Sequential agents - Agent Development Kit - Google, accessed June 9, 2025](https://google.github.io/adk-docs/agents/workflow-agents/sequential-agents/)
3. [Agent Development Kit: Making it easy to build multi-agent applications, accessed June 9, 2025](https://developers.googleblog.com/en/agent-development-kit-easy-to-build-multi-agent-applications/)
4. [Develop an Agent Development Kit agent | Generative AI on Vertex AI - Google Cloud, accessed June 9, 2025](https://cloud.google.com/vertex-ai/generative-ai/docs/agent-engine/develop/adk)
5. [Manage sessions with Agent Development Kit | Generative AI on Vertex AI - Google Cloud, accessed June 9, 2025](https://cloud.google.com/vertex-ai/generative-ai/docs/agent-engine/sessions/manage-sessions-adk)
6. [Quickstart: Build an agent with the Agent Development Kit | Generative AI on Vertex AI, accessed June 9, 2025](https://cloud.google.com/vertex-ai/generative-ai/docs/agent-development-kit/quickstart)
