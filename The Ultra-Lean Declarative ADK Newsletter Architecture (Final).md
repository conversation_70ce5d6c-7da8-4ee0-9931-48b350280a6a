## **1\. Guiding Philosophy: Ultimate Agentic Purity via Declarative ADK Primitives**

This architecture represents the zenith of simplicity and elegance by constructing the entire newsletter generation pipeline from declarative Google Agent Development Kit (ADK) workflow agents (SequentialAgent, LoopAgent, ParallelAgent). Control flow is governed by ADK's final\_answer mechanism, and imperative Python orchestration is confined to a single, minimal BaseAgent for dynamic fan-out and subsequent aggregation. This design prioritizes agentic reasoning and minimizes custom code, allowing ADK's inherent capabilities to manage the workflow.

* **final\_answer as the Universal Loop Terminator:** Critic/reviewer agents within LoopAgents utilize auto\_mark\_final=True. When their output begins with a predefined prefix (e.g., "FINAL STORY ARRAY:"), ADK automatically sets final\_answer=True, concluding the loop. The critic's full response, which contains the structured data (e.g., a JSON array) or final content, becomes the loop's definitive output.  
* **Ultra-Lean State Surface:** Only three primary ADK state keys are used for handoffs between major stages: stories\_array (a JSON array of story topic strings), story\_bundle (aggregated research text), and newsletter. The initial user\_request is the fourth key, initiating the process. Agents read from these, and the designated final agent in each stage writes its conclusive output directly to the subsequent key.  
* **Declarative Orchestration:** A top-level SequentialAgent (ManagingEditor) executes the main stages. Its definition is a pure list of ADK agent instances. 1  
* **Agentic Data Transformation & Direct Aggregation:**  
  * The StoryListCritic is prompted to output a JSON array directly for stories\_array. An optional JsonRepairAgent can be added for robustness if LLM output for JSON is inconsistent.  
  * Individual DeepResearchCritic\_i agents in the parallel stage write their final research to unique temporary state keys (e.g., state.story\_part\_i). The FanOutResearchAgent then collects these parts *and directly joins them into state.story\_bundle*, eliminating a separate aggregation agent and state key.  
* **Minimal Custom Code for Dynamic Parallelism & Aggregation:** A single, lean custom FanOutResearchAgent (BaseAgent) handles the dynamic creation and execution of parallel research loops, followed by the collection and joining of their outputs. Its Python code is minimal (target \<\<20 lines for the core orchestration logic).  
* **Focused Configurability:** Control is primarily via max\_iterations on LoopAgents (relying on ADK defaults like 3 where sensible, or specifying higher values for tasks like final newsletter polishing) and standard LlmAgent configurations (model selection via LiteLLM, token limits, temperature, etc.).

## **2\. The Purely Declarative Pipeline: Three Stages, Four Core Keys**

The ManagingEditor is a SequentialAgent orchestrating these stages.

**Core State Keys:**

1. user\_request: (Input) Raw user query.  
2. stories\_array: (Output of Stage 1\) A (potentially validated) JSON array of story topic strings.  
3. story\_bundle: (Output of Stage 2\) Aggregated research text for all stories.  
4. newsletter: (Output of Stage 3\) The final newsletter.

### ---

**Stage 1: Story Identification & JSON Array Output**

1. **WideSearchLoop (LoopAgent)**  
   * **Purpose:** Identify a preliminary list of relevant stories and output them as a JSON array string.  
   * **Generator:** WideSearchAgent (LlmAgent)  
     * Reads: state.user\_request.  
     * Task: Runs broad searches (e.g., using a GoogleSearchTool), produces a textual list of potential stories.  
     * Writes: state.current\_story\_list\_text\_draft (intermediate state key, internal to the loop).  
   * **Critic:** StoryListCritic (LlmAgent)  
     * Reads: state.user\_request, state.current\_story\_list\_text\_draft.  
     * Task: Checks coverage, recency, duplication. If good, replies with "FINAL STORY ARRAY:" followed by the refined list formatted *as a JSON array string*. The prompt must strictly enforce this JSON output. (ADK's output\_format="json" on the agent can be used if available and effective, otherwise strong prompting is key).  
     * auto\_mark\_final=True (ADK sets final\_answer=True if the output starts with the prefix).  
     * output\_key="stories\_array" (Writes the JSON array string directly to the main state).  
   * max\_iterations: ADK default (e.g., 3\) or specified.  
2. **JsonRepairAgent (LlmAgent) (Optional Stage)**  
   * **Purpose:** Ensure the story list is valid JSON, if the StoryListCritic proves unreliable in consistently producing perfect JSON.  
   * **Input:** Reads state.stories\_array (which might be imperfect JSON from the previous step).  
   * **Task:** Validates and, if necessary, repairs the input string to be a valid JSON array of strings. Prompted to output *only* the valid JSON array.  
   * output\_format="json" (or strict prompting).  
   * **Output:** Overwrites state.stories\_array with the validated JSON array string.  
   * This is a single-turn agent (no loop).

### ---

**Stage 2: Parallel Deep Research & Direct Bundle Aggregation**

* **FanOutResearchAgent (Custom BaseAgent \- Minimal Python)**  
  * **Input:** Reads state.stories\_array (a JSON array string), state.user\_request.  
  * **Task:**  
    1. Parses state.stories\_array (e.g., using json.loads()) into a Python list of topic strings.  
    2. For each story\_topic and its index (i) in the parsed list:  
       * Instantiates a DeepResearchLoop\_i (LoopAgent).  
         * **Generator:** DeepResearcherAgent\_i (LlmAgent)  
           * Input: story\_topic (passed via constructor/config to the agent instance), state.user\_request.  
           * Task: Deep research on the specific story\_topic using appropriate tools (e.g., web search with lookback\_period).  
           * Writes: state.current\_deep\_research\_draft\_\[i\] (intermediate).  
         * **Critic:** DeepResearchCritic\_i (LlmAgent)  
           * Input: state.current\_deep\_research\_draft\_\[i\], story\_topic.  
           * Task: Validates depth, accuracy, citations. If good, replies "FINAL ANALYSIS STORY {i}:" followed by the research text.  
           * auto\_mark\_final=True.  
           * output\_key=f"story\_part\_{i}" (Writes its final research to a unique temporary state key).  
         * max\_iterations: ADK default or specified (e.g., N\_deep\_research). Consider if a single, self-correcting agent per story (generator that revises until perfect then prefixes "FINAL...") is preferred over a generator/critic pair to reduce LLM calls, based on cost vs. clarity.  
    3. Executes all instantiated DeepResearchLoop\_i instances concurrently using an internally managed ParallelAgent.  
    4. After the ParallelAgent completes, this FanOutResearchAgent collects all state.story\_part\_i values.  
    5. It then joins these collected parts into a single string and writes it directly: session.state\["story\_bundle"\] \= "\\n\\n---\\n\\n".join(collected\_story\_parts).  
  * **Output:** No single output key for the agent itself; its primary side effect is populating state.story\_bundle.

### ---

**Stage 3: Newsletter Generation**

* **NewsletterLoop (LoopAgent)**  
  * **Purpose:** Draft and review the final newsletter.  
  * **Generator:** NewsletterWriterAgent (LlmAgent)  
    * Reads: state.user\_request, state.story\_bundle.  
    * Task: Drafts the newsletter according to the user's requirements and the researched content.  
    * Writes: state.current\_newsletter\_draft (intermediate).  
  * **Critic:** NewsletterReviewerAgent (LlmAgent)  
    * Reads: state.user\_request, state.story\_bundle, state.current\_newsletter\_draft.  
    * Task: Reviews for accuracy, style, tone, completeness. If good, replies "FINAL NEWSLETTER:" followed by the complete newsletter.  
    * auto\_mark\_final=True.  
    * output\_key="newsletter" (Writes the final newsletter directly to the main state).  
  * max\_iterations: Specified (e.g., M\_newsletter, potentially higher than default if more polish iterations are expected).

## ---

**3\. The ManagingEditor (Top-Level Orchestrator)**

The ManagingEditor is a SequentialAgent whose sub\_agents list defines the entire pipeline declaratively.

Python

\# Conceptual Python definition of the ManagingEditor and its components  
\# (Actual ADK definition might be more declarative if using a config file format,  
\# or this Python code would be used to construct the agent graph)

from google.adk.agents import SequentialAgent, LlmAgent, LoopAgent, BaseAgent, ParallelAgent  
from google.adk.agents.invocation\_context import InvocationContext \# Corrected import for Session/InvocationContext  
from google.adk.events import Event, EventActions  
from typing import AsyncIterator, List \# For type hinting  
import json

\# \--- Define Individual LlmAgents (Generators and Critics) \---  
\# (Assuming model, tools, generate\_content\_config are defined elsewhere or passed in)

\# Stage 1 Agents  
wide\_search\_agent \= LlmAgent(  
    name="WideSearchAgent",  
    system\_prompt="Read state.user\_request. Run broad web searches. Produce a bulleted list of relevant story ideas. Write this list to state.current\_story\_list\_text\_draft.",  
    \# tools=, \# Example  
    output\_key="current\_story\_list\_text\_draft"  
)  
story\_list\_critic \= LlmAgent(  
    name="StoryListCritic",  
    system\_prompt="Read state.user\_request and state.current\_story\_list\_text\_draft. Check for coverage, recency, and duplication. If the list is good and complete, reply with 'FINAL STORY ARRAY:' followed by the refined list as a JSON array string (e.g., 'FINAL STORY ARRAY:'). Otherwise, provide concrete feedback for WideSearchAgent to improve the list.",  
    auto\_mark\_final=True,  
    output\_key="stories\_array" \# Directly writes the JSON string  
)  
wide\_search\_loop \= LoopAgent(  
    name="WideSearchLoop",  
    sub\_agents=\[wide\_search\_agent, story\_list\_critic\],  
    max\_iterations=3 \# Example, ADK default is often 3  
)

\# Optional JsonRepairAgent (if needed for robustness)  
json\_repair\_agent \= LlmAgent(  
    name="JsonRepairAgent",  
    system\_prompt="Read state.stories\_array. If it is not a valid JSON array of strings, repair it. Reply ONLY with the valid JSON array string. If it is already valid, reply with the original content.",  
    \# output\_format="json", \# If ADK supports this for LlmAgent and it's reliable  
    output\_key="stories\_array" \# Overwrites with repaired (or original) JSON  
)

\# Stage 2: FanOutResearchAgent (Custom BaseAgent)  
class FanOutResearchAgent(BaseAgent):  
    def \_\_init\_\_(self, name="FanOutResearchAgent", max\_iterations\_per\_story=3, \*\*kwargs):  
        super().\_\_init\_\_(name=name, \*\*kwargs)  
        self.max\_iterations\_per\_story \= max\_iterations\_per\_story  
        \# Tool instances (e.g., search tool) would be configured and passed to DeepResearcherAgent\_i instances

    async def \_run\_async\_impl(self, session: InvocationContext, \*\*kwargs) \-\> AsyncIterator\[Event\]:  
        user\_request \= session.state.get("user\_request", "")  
        stories\_array\_str \= session.state.get("stories\_array", "")  
          
        individual\_story\_topics: List\[str\] \=  
        try:  
            parsed\_json \= json.loads(stories\_array\_str)  
            if isinstance(parsed\_json, list) and all(isinstance(item, str) for item in parsed\_json):  
                individual\_story\_topics \= parsed\_json  
            else:  
                \# Attempt to re-parse with an LLM if primary parsing fails or is not a list of strings  
                \# This is a fallback, strong prompting for StoryListCritic is preferred.  
                \# For this example, we'll proceed with an empty list if parsing is problematic.  
                print(f"Warning: stories\_array was not a valid list of strings: {stories\_array\_str}")  
        except json.JSONDecodeError:  
            print(f"Error: Failed to decode stories\_array JSON: {stories\_array\_str}")  
            \# Potentially invoke an LLM to attempt to parse/fix stories\_array\_str here if critical  
            \# For now, we'll proceed with an empty list, leading to an empty story\_bundle.

        if not individual\_story\_topics:  
            session.state\["story\_bundle"\] \= "No stories were identified or parsed from stories\_array for deep research."  
            yield Event(action=EventActions.UPDATE\_STATE, data={"story\_bundle": session.state\["story\_bundle"\]})  
            return

        deep\_research\_loops \=  
        for i, story\_topic in enumerate(individual\_story\_topics):  
            \# Define tools for the researcher agent, potentially configured with lookback\_period  
            \# research\_tool \= SomeSearchTool(lookback\_period=session.config.get("research\_lookback\_period"))  
            researcher \= LlmAgent(  
                name=f"DeepResearcher\_{i}",  
                system\_prompt=f"You are a deep research agent. Your current task is to research the story: '{story\_topic}'. Use your search tool. The overall user request for context is: '{user\_request}'. Write your findings to state.current\_deep\_research\_draft\_{i}.",  
                \# tools=\[research\_tool\], \# Example tool  
                output\_key=f"current\_deep\_research\_draft\_{i}"  
            )  
            critic \= LlmAgent(  
                name=f"DeepResearchCritic\_{i}",  
                system\_prompt=f"Review the research in state.current\_deep\_research\_draft\_{i} for the story '{story\_topic}'. Check for depth, accuracy, and citations. If excellent, reply 'FINAL ANALYSIS STORY {i}:' followed by the complete research text. Otherwise, provide specific feedback.",  
                auto\_mark\_final=True,  
                output\_key=f"story\_part\_{i}" \# Each critic writes to a unique key  
            )  
            story\_loop \= LoopAgent(  
                name=f"DeepResearchLoop\_{i}",  
                sub\_agents=\[researcher, critic\],  
                max\_iterations=self.max\_iterations\_per\_story  
            )  
            deep\_research\_loops.append(story\_loop)  
          
        if not deep\_research\_loops:  
            session.state\["story\_bundle"\] \= "No research loops were created (this should not happen if stories were parsed)."  
            yield Event(action=EventActions.UPDATE\_STATE, data={"story\_bundle": session.state\["story\_bundle"\]})  
            return

        \# Define the ParallelAgent to run all the story\_loops concurrently  
        parallel\_research\_executor \= ParallelAgent(  
            name="StoryParallelResearchExecutor",  
            sub\_agents=deep\_research\_loops  
        )

        \# Yield events from the parallel executor as it runs  
        async for event in parallel\_research\_executor.arun(session=session):  
            yield event

        \# After all parallel loops complete, collect their outputs and join them  
        collected\_story\_parts \=  
        for i in range(len(individual\_story\_topics)):  
            \# The output from each DeepResearchCritic\_i is in state\[f"story\_part\_{i}"\]  
            \# ADK's LoopAgent, when its final sub-agent (the critic) has final\_answer=True,  
            \# will have its output (the critic's output) available.  
            \# If ParallelAgent makes these directly accessible or if they are reliably in session.state:  
            part\_content \= session.state.get(f"story\_part\_{i}", "")  
            if part\_content:  
                \# Remove the "FINAL ANALYSIS STORY i:" prefix before appending  
                prefix\_to\_remove \= f"FINAL ANALYSIS STORY {i}:" \# Note: index \`i\` is 0-based  
                if part\_content.startswith(prefix\_to\_remove):  
                    part\_content \= part\_content\[len(prefix\_to\_remove):\].strip()  
                collected\_story\_parts.append(part\_content)  
          
        session.state\["story\_bundle"\] \= "\\n\\n---\\n\\n".join(collected\_story\_parts)  
        yield Event(action=EventActions.UPDATE\_STATE, data={"story\_bundle": session.state\["story\_bundle"\]})

fan\_out\_research\_agent \= FanOutResearchAgent() \# Instantiate the custom agent

\# Stage 3 Agents  
newsletter\_writer\_agent \= LlmAgent(  
    name="NewsletterWriterAgent",  
    system\_prompt="Read state.user\_request and state.story\_bundle. Draft a compelling newsletter based on this information. Write the draft to state.current\_newsletter\_draft.",  
    output\_key="current\_newsletter\_draft"  
)  
newsletter\_reviewer\_agent \= LlmAgent(  
    name="NewsletterReviewerAgent",  
    system\_prompt="Read state.user\_request, state.story\_bundle, and state.current\_newsletter\_draft. Review for accuracy, style, tone, and completeness. If excellent, reply 'FINAL NEWSLETTER:' followed by the complete newsletter. Otherwise, provide revision notes.",  
    auto\_mark\_final=True,  
    output\_key="newsletter"  
)  
newsletter\_loop \= LoopAgent(  
    name="NewsletterLoop",  
    sub\_agents=\[newsletter\_writer\_agent, newsletter\_reviewer\_agent\],  
    max\_iterations=5 \# Example, might need more iterations for polish  
)

\# Define the ManagingEditor SequentialAgent  
\# The optional JsonRepairAgent can be added here if testing shows it's necessary.  
sub\_agent\_list\_for\_managing\_editor \= \[  
    wide\_search\_loop,  
    \# json\_repair\_agent, \# Uncomment if using  
    fan\_out\_research\_agent,  
    newsletter\_loop  
\]

ManagingEditor \= SequentialAgent(  
    name="ManagingEditorPipeline",  
    sub\_agents=sub\_agent\_list\_for\_managing\_editor  
)

\# \--- To run this (conceptual, assuming an AdkApp setup) \---  
\# from vertexai.preview.reasoning\_engines import AdkApp  
\# app \= AdkApp(agent=ManagingEditor)  
\#  
\# async def run\_pipeline():  
\#     initial\_session\_state \= {"user\_request": "Tech news on AI and climate change policy."}  
\#     \# For local testing with AdkApp, you might use app.agent\_runner  
\#     \# result\_context \= await app.agent\_runner.run\_agent(ManagingEditor, initial\_session\_state)  
\#     \# For direct agent running if AdkApp is not the entry point:  
\#     final\_context \= InvocationContext(state=initial\_session\_state)  
\#     async for \_ in ManagingEditor.arun(session=final\_context):  
\#         pass \# Consume events  
\#     newsletter\_output \= final\_context.state.get("newsletter")  
\#     print("--- FINAL NEWSLETTER \---")  
\#     print(newsletter\_output)  
\#  
\# if \_\_name\_\_ \== "\_\_main\_\_":  
\#     import asyncio  
\#     asyncio.run(run\_pipeline())

## **4\. Peak Purity and Practical Robustness**

This architecture achieves the "North Star" with practical safeguards:

* **Native Loop Control:** final\_answer via auto\_mark\_final=True is standard for all loops, simplifying termination logic.  
* **Agent-Driven Data Formatting (with Optional Safeguard):** The StoryListCritic is prompted to output JSON directly. The JsonRepairAgent is an optional, single-turn addition for robustness if the critic's JSON output isn't consistently perfect.  
* **Safe Parallel Aggregation:** The FanOutResearchAgent orchestrates parallel research. Each DeepResearchCritic\_i writes its final output to a unique story\_part\_i state key. The FanOutResearchAgent then collects these distinct parts *after* parallel execution completes and joins them into state.story\_bundle. This approach is inherently race-condition-free.  
* **Minimal Custom Code:** The FanOutResearchAgent is the *only* component with imperative Python. Its role is strictly limited to:  
  1. Parsing the stories\_array.  
  2. Dynamically instantiating DeepResearchLoop\_i sub-agents.  
  3. Invoking these loops via an internal ParallelAgent.  
  4. Collecting the uniquely keyed results from the state and joining them into story\_bundle. This represents the irreducible minimum of procedural logic required for this dynamic fan-out-and-gather pattern, keeping the custom code footprint exceptionally small.  
* **Declarative Core:** The overall pipeline (ManagingEditor) is a clear, declarative sequence of ADK's powerful workflow agents, promoting maintainability and readability. 1  
* **Deployment Path:** This ADK-based system is designed with a clear path from local development (using, for example, InMemorySessionService) to scalable cloud deployment on platforms like Vertex AI Agent Engine (using VertexAiSessionService). 3

This design is exceptionally lean, robust for real ADK runs, and maximizes the framework's inherent capabilities while minimizing custom code. It directly reflects the "ultra-lean state surface" of three primary handoff keys (stories\_array, story\_bundle, newsletter) plus the initial user\_request.

#### **Works cited**

1. Multi-Agent Systems in ADK \- Google, accessed June 9, 2025, [https://google.github.io/adk-docs/agents/multi-agents/](https://google.github.io/adk-docs/agents/multi-agents/)  
2. Sequential agents \- Agent Development Kit \- Google, accessed June 9, 2025, [https://google.github.io/adk-docs/agents/workflow-agents/sequential-agents/](https://google.github.io/adk-docs/agents/workflow-agents/sequential-agents/)  
3. Agent Development Kit: Making it easy to build multi-agent applications, accessed June 9, 2025, [https://developers.googleblog.com/en/agent-development-kit-easy-to-build-multi-agent-applications/](https://developers.googleblog.com/en/agent-development-kit-easy-to-build-multi-agent-applications/)  
4. Develop an Agent Development Kit agent | Generative AI on Vertex AI \- Google Cloud, accessed June 9, 2025, [https://cloud.google.com/vertex-ai/generative-ai/docs/agent-engine/develop/adk](https://cloud.google.com/vertex-ai/generative-ai/docs/agent-engine/develop/adk)  
5. Manage sessions with Agent Development Kit | Generative AI on Vertex AI \- Google Cloud, accessed June 9, 2025, [https://cloud.google.com/vertex-ai/generative-ai/docs/agent-engine/sessions/manage-sessions-adk](https://cloud.google.com/vertex-ai/generative-ai/docs/agent-engine/sessions/manage-sessions-adk)  
6. Quickstart: Build an agent with the Agent Development Kit | Generative AI on Vertex AI, accessed June 9, 2025, [https://cloud.google.com/vertex-ai/generative-ai/docs/agent-development-kit/quickstart](https://cloud.google.com/vertex-ai/generative-ai/docs/agent-development-kit/quickstart)