# Anti-Patterns and Validation Guide

## What This Document Is For

Prevent future developers from implementing anti-patterns that violate our core agentic principles. This document consolidates all validation requirements and examples from across the project.

## What You'll Accomplish

- Understand ALL anti-patterns that MUST be avoided across the entire system
- Access consolidated validation checklists for any implementation
- See comprehensive examples of violations and their correct alternatives
- Implement proper error handling, context propagation, and orchestration patterns
- Use as single reference for all anti-pattern prevention

## 🚨 CRITICAL ANTI-PATTERNS THAT MUST BE AVOIDED

### Core Principle Violations

**These anti-patterns violate our fundamental agentic principles and MUST be avoided in ALL implementations:**

1. **Hardcoded Dictionary Return Structures** - Violates trust in LLM intelligence
2. **Context Isolation** - Ignores session state and user requirements
3. **Agent Duplication** - Recreates existing proven functionality
4. **Overengineering with Custom Wrappers** - Fights against ADK framework capabilities
5. **Silent Error Handling** - Masks problems instead of explicit validation
6. **Newsletter-Specific Agent Creation (i.e. applicable for only for one type of newsletter)** - Violates universal flexibility principle
7. **Programmatic Quality Assessment** - Uses hardcoded criteria over LLM judgment

---

### ANTI-PATTERN 1: Hardcoded Dictionary Return Structures

**Problem**: Tools/functions that return predetermined dictionary structures violate agentic principles by not trusting LLM intelligence.

#### ❌ WRONG Examples

```python
# ❌ WRONG - Hardcoded quality assessment
def assess_quality(research_results: str) -> dict:
    return {
        "status": "sufficient",      # Hardcoded keys
        "quality_level": "high",     # Predetermined structure
        "score": 8.5,               # Numerical scoring
        "meets_standards": True,     # Boolean flags
        "recommendations": [...]     # Fixed schema
    }

# ❌ WRONG - Structured parsing of LLM responses
def parse_llm_assessment(llm_response: str) -> dict:
    # Trying to extract structured data from natural language
    if "sufficient" in llm_response.lower():
        return {"status": "pass", "quality": "high"}
    else:
        return {"status": "fail", "quality": "low"}
```

#### ✅ CORRECT Approach

```python
# ✅ CORRECT - Trust LLM intelligence completely
async def assess_quality_agentic(user_requirements: str, research_results: str) -> str:
    prompt = f"""
    Evaluate this research against user requirements:
    
    Requirements: {user_requirements}
    Research: {research_results}
    
    Respond with "SUFFICIENT" or "NEEDS_IMPROVEMENT" followed by your reasoning.
    """
    
    llm_response = await llm.agenerate([{"role": "user", "content": prompt}])
    return llm_response.generations[0][0].text  # Return raw LLM assessment

# ✅ CORRECT - Simple pass/fail determination
def is_quality_sufficient(llm_assessment: str) -> bool:
    return llm_assessment.upper().startswith("SUFFICIENT")
```

### ANTI-PATTERN 2: Context Isolation

**Problem**: Agents that ignore session state context instead of properly propagating user requirements.

#### ❌ WRONG Examples

```python
# ❌ WRONG - Agent operates in isolation
class IsolatedQualityAgent(BaseAgent):
    async def _run_async_impl(self, ctx):
        # Ignores user_requirements from session state
        generic_assessment = "Research looks good"
        return generic_assessment

# ❌ WRONG - Using fallback values instead of validation
class SilentFallbackAgent(BaseAgent):
    async def _run_async_impl(self, ctx):
        # Silent fallback masks missing context
        user_requirements = ctx.session.state.get("user_requirements", "default requirements")
```

#### ✅ CORRECT Approach

```python
# ✅ CORRECT - Context-aware agent with explicit validation
class ContextAwareQualityAgent(BaseAgent):
    async def _run_async_impl(self, ctx):
        # MANDATORY: Validate required context first
        user_requirements = ctx.session.state.get("user_requirements", "")
        research_results = ctx.session.state.get("research_results", "")
        
        # MANDATORY: Explicit error handling for missing inputs
        if not user_requirements:
            raise ValueError("No user requirements found for quality assessment")
        if not research_results:
            raise ValueError("No research results found for quality assessment")
        
        # Use context in assessment logic
        assessment = await self.evaluate_against_requirements(user_requirements, research_results)
        
        yield Event(actions=EventActions(
            state_delta={"quality_assessment": assessment},
            escalate=self.is_sufficient(assessment)
        ))
```

### ANTI-PATTERN 3: Agent Duplication

**Problem**: Creating new agents that duplicate existing functionality instead of orchestrating proven agents.

#### ❌ WRONG Examples

```python
# ❌ WRONG - Newsletter-specific agents duplicate functionality
class DailyNewsAgent(LlmAgent):
    def __init__(self):
        super().__init__(
            instruction="Research daily news topics...",  # Duplicates WideSearchAgent
            tools=[exa_wide_search]
        )

class BiotechVCAgent(LlmAgent):
    def __init__(self):
        super().__init__(
            instruction="Research biotech companies...",  # Duplicates DeepSearchAgent
            tools=[exa_deep_search]
        )

# ❌ WRONG - Modifying existing agents
wide_search_agent.instruction = "Focus only on technology..."  # Breaks backward compatibility
```

#### ✅ CORRECT Approach

```python
# ✅ CORRECT - Orchestrate existing agents with context injection
from underlines_adk.agents.wide_search_agent import exa_agent as wide_search_agent
from underlines_adk.agents.deep_search_agent import exa_agent as deep_search_agent

class ContextAwareAgentWrapper(BaseAgent):
    def __init__(self, wrapped_agent, user_requirements):
        super().__init__(name=f"ContextAware{wrapped_agent.name}")
        self.wrapped_agent = wrapped_agent
        self.user_requirements = user_requirements

    async def _run_async_impl(self, ctx):
        # Inject context into session state
        yield Event(actions=EventActions(state_delta={
            "user_requirements": self.user_requirements
        }))

        # Run existing agent with proper context
        async for event in self.wrapped_agent.run_async(ctx):
            yield event

# Use through orchestration
research_coordinator = ParallelAgent(sub_agents=[
    ContextAwareAgentWrapper(wide_search_agent, requirements, plan),
    ContextAwareAgentWrapper(deep_search_agent, requirements, plan)
])
```

### ANTI-PATTERN 4: Overengineering with Custom Wrappers

**Problem**: Creating unnecessary custom BaseAgent wrappers when ADK provides native solutions.

#### ❌ WRONG Examples

```python
# ❌ WRONG - Unnecessary ContextAwareAgentWrapper (REAL EXAMPLE FROM OUR CODEBASE)
class ContextAwareAgentWrapper(BaseAgent):
    def __init__(self, wrapped_agent, requirements, plan, focus_area):
        super().__init__(
            name=agent_name,
            description=f"Context-aware wrapper for {wrapped_agent.name}",
            sub_agents=[wrapped_agent],
            output_key=wrapped_agent.output_key
        )
        self.wrapped_agent = wrapped_agent
        self.user_requirements = requirements

    async def _run_async_impl(self, ctx):
        # Manual context injection - UNNECESSARY!
        context_injection = {
            "user_requirements": self.user_requirements,
            "current_focus": self.focus_area
        }

        # Manual Event/EventActions - FIGHTING THE FRAMEWORK!
        yield Event(actions=EventActions(state_delta=context_injection))

        # Wrapper delegation - ADDS NO VALUE!
        async for event in self.wrapped_agent.run_async(ctx):
            yield event

# ❌ WRONG - Using the wrapper in coordination
context_aware_agent = self._create_context_aware_agent(
    base_agent=wide_search_agent,
    agent_name="ContextAwareWideSearch",
    user_requirements=user_requirements,
    research_plan=research_plan,
    focus="broad coverage"
)
```

**Why This Is Wrong:**

- ADK agents automatically access `ctx.session.state` - no manual injection needed
- Creates 40+ lines of unnecessary wrapper code
- Fights against ADK's built-in session state management
- Adds complexity without any functional benefit

#### ✅ CORRECT Approach

```python
# ✅ CORRECT - ADK-native direct coordination
async def _run_async_impl(self, ctx):
    # Validate required session state (ADK pattern)
    user_requirements = ctx.session.state.get("user_requirements", "")

    # Explicit error handling for missing inputs
    if not user_requirements:
        raise ValueError("No user requirements found for research coordination")

    # ADK-native direct agent coordination (no wrappers needed)
    # Existing agents read session state automatically via their instructions
    research_coordinator = ParallelAgent(
        name="DirectResearchCoordination",
        sub_agents=[
            wide_search_agent,  # Reads session state natively
            deep_search_agent   # Reads session state natively
        ]
    )

    # Execute direct coordination using ADK's built-in orchestration
    async for event in research_coordinator.run_async(ctx):
        yield event
```

**Why This Is Correct:**

- Trusts ADK's built-in session state management
- Uses ParallelAgent for direct coordination (ADK-native)
- Eliminates unnecessary wrapper complexity
- Follows "trust the framework" philosophy

### ANTI-PATTERN 5: Silent Error Handling

**Problem**: Using fallback values or silent error handling instead of explicit validation and clear error messages.

#### ❌ WRONG Examples

```python
# ❌ WRONG - Silent fallbacks mask problems
def get_user_requirements_with_fallback(ctx):
    return ctx.session.state.get("user_requirements", "default requirements")

# ❌ WRONG - Generic error handling
try:
    result = some_operation()
except Exception:
    return "something went wrong"  # Unhelpful error message

# ❌ WRONG - Continuing with incomplete data
if not user_requirements:
    user_requirements = "generate a newsletter"  # Fallback value
```

#### ✅ CORRECT Approach

```python
# ✅ CORRECT - Explicit validation with clear errors
def get_required_state(ctx, key: str, error_message: str = None) -> str:
    value = ctx.session.state.get(key, "")
    if not value:
        error_msg = error_message or f"Required state key '{key}' not found"
        raise ValueError(error_msg)
    return value

# ✅ CORRECT - Specific error handling
async def _run_async_impl(self, ctx):
    try:
        user_requirements = get_required_state(
            ctx, "user_requirements",
            "No user requirements found for research coordination"
        )
    except ValueError as e:
        # Re-raise with context about which agent failed
        raise ValueError(f"{self.name}: {str(e)}")
```

### ANTI-PATTERN 6: Newsletter-Specific Agent Creation

**Problem**: Creating agents with hardcoded newsletter-specific logic instead of universal flexibility.

#### ❌ WRONG Examples

```python
# ❌ WRONG - Newsletter-specific agents violate universal flexibility
class DailyNewsAgent(LlmAgent):
    def __init__(self):
        super().__init__(
            instruction="Research daily news topics...",  # Duplicates WideSearchAgent
            tools=[exa_wide_search]
        )

class BiotechVCAgent(LlmAgent):
    def __init__(self):
        super().__init__(
            instruction="Research biotech companies...",  # Duplicates DeepSearchAgent
            tools=[exa_deep_search]
        )
```

**Why This Is Wrong:**

- Duplicates existing proven functionality
- Breaks backward compatibility
- Creates maintenance burden
- Violates universal flexibility principle

#### ✅ CORRECT Approach

```python
# ✅ CORRECT - Use existing universal agents through orchestration
from underlines_adk.agents.wide_search_agent import exa_agent as wide_search_agent
from underlines_adk.agents.deep_search_agent import exa_agent as deep_search_agent

# Orchestrate existing agents for any newsletter type
research_pipeline = SequentialAgent(sub_agents=[wide_search_agent, deep_search_agent])
```

### ANTI-PATTERN 7: Programmatic Quality Assessment

**Problem**: Using numerical scoring or hardcoded criteria instead of LLM judgment for quality assessment.

#### ❌ WRONG Examples

```python
# ❌ WRONG - Programmatic quality assessment
def assess_quality_programmatic(research_results: str) -> dict:
    word_count = len(research_results.split())
    citation_count = research_results.count('[')

    if word_count > 1000 and citation_count > 5:
        return {"status": "sufficient", "score": 8.5}
    else:
        return {"status": "insufficient", "score": 4.2}
```

#### ✅ CORRECT Approach

```python
# ✅ CORRECT - LLM-driven quality assessment
class QualityAssessmentAgent(BaseAgent):
    async def _run_async_impl(self, ctx):
        # Use LLM judgment for quality assessment
        assessment = await llm.agenerate([{"role": "user", "content": prompt}])
        is_sufficient = assessment.upper().startswith("SUFFICIENT")

        yield Event(actions=EventActions(
            state_delta={"quality_assessment": assessment},
            escalate=is_sufficient
        ))
```

## 🚨 MANDATORY VALIDATION CHECKLISTS

### Pre-Implementation Validation

**Run this checklist BEFORE writing ANY code:**

- [ ] **Agentic Intelligence**: Does this trust LLM judgment over programmatic logic?
- [ ] **Context Propagation**: Does this read and validate session state properly?
- [ ] **Orchestration**: Does this use existing agents instead of duplicating functionality?
- [ ] **Error Handling**: Does this raise explicit errors instead of silent fallbacks?
- [ ] **Universal Flexibility**: Does this work for ANY newsletter type without modification?
- [ ] **ADK-Native Patterns**: Does this leverage ADK's built-in capabilities instead of creating custom abstractions?

### Tool/Function Implementation Validation

**For ANY tool or function:**

- [ ] **No Hardcoded Returns**: Avoids predetermined dictionary structures?
- [ ] **LLM Intelligence**: Returns raw LLM responses instead of parsed data?
- [ ] **Natural Language**: Works with natural language input/output?
- [ ] **Clear Errors**: Raises ValueError for missing required inputs?

### Agent Implementation Validation

**For ANY agent:**

- [ ] **Context Awareness**: Reads user_requirements from session state?
- [ ] **Explicit Validation**: Raises ValueError for missing required inputs?
- [ ] **No Duplication**: Orchestrates existing agents instead of rebuilding functionality?
- [ ] **Agentic Principles**: Trusts LLM intelligence over programmatic decision trees?
- [ ] **No Overengineering**: Avoids unnecessary custom BaseAgent wrappers when ADK provides native solutions?

### Quality Assessment Validation

**For ANY quality assessment:**

- [ ] **LLM-Driven**: Uses LLM judgment instead of numerical scoring?
- [ ] **Natural Language**: Returns LLM's raw assessment without parsing?
- [ ] **User-Specific**: Evaluates against user's stated requirements?
- [ ] **No Fixed Criteria**: Avoids hardcoded quality standards?
- [ ] **Per-Story Timing**: Happens during deep research phase, not after all research completes?

### Story-Centric Workflow Validation

**For story-centric newsletter pipeline implementation (as defined in [PRD.md](prd.md)):**

- [ ] **Multi-Stage Coordination**: DynamicResearchCoordinator implements 3 distinct phases (Story Discovery → Story Validation & Regrouping → Deep Research Per Story)?
- [ ] **Story Discovery First**: Stories discovered and validated BEFORE deep research begins?
- [ ] **Per-Story Quality**: Each story gets its own LoopAgent with DeepSearchAgent + QualityAssessmentAgent?
- [ ] **Session State Progression**: Uses PRD-defined session state keys in correct sequence?
- [ ] **Proper Handoffs**: DynamicResearchCoordinator completes all research before FlexibleNewsletterSynthesizer begins?
- [ ] **Quality Assessment Timing**: Quality assessment happens PER STORY during deep research phase, not globally after all research completes?
- [ ] **Universal Flexibility**: Works for any newsletter type without hardcoded logic?

## Context Propagation Requirements

### Mandatory Session State Management

**ALL agents MUST follow these patterns and use PRD-defined session state keys:**

```python
# MANDATORY: Read required context with validation using PRD session state keys
user_requirements = ctx.session.state.get("user_requirements", "")
topic_areas = ctx.session.state.get("topic_areas", [])
discovered_stories = ctx.session.state.get("discovered_stories", [])
final_story_list = ctx.session.state.get("final_story_list", [])
story_research_outputs = ctx.session.state.get("story_research_outputs", [])
final_newsletter = ctx.session.state.get("final_newsletter", "")

# MANDATORY: Explicit error handling for missing context
if not user_requirements:
    raise ValueError("No user requirements found for [agent_name]")
```

### PRD-Defined Session State Keys

**MUST use these exact keys as defined in PRD.md:**

- `user_requirements`: Original natural language newsletter requirements
- `topic_areas`: Identified topic areas for story discovery
- `discovered_stories`: Initial story discoveries from WideSearchAgent
- `final_story_list`: Validated and regrouped stories for deep research
- `story_research_outputs`: Completed research for each story
- `final_newsletter`: FlexibleNewsletterSynthesizer's formatted output

### Context Injection Pattern

**For orchestrating existing agents:**

```python
# Inject context before running existing agents
yield Event(actions=EventActions(state_delta={
    "user_requirements": self.user_requirements,
    "research_plan": self.research_plan,
    "current_focus": self.focus_area
}))

# Then run existing agent with proper context
async for event in existing_agent.run_async(ctx):
    yield event
```

## Implementation Validation Process

### Step 1: Design Review

Before writing code, validate your design:

1. Does this approach trust LLM intelligence?
2. Does this orchestrate existing agents?
3. Does this handle context properly?
4. Does this raise explicit errors?

### Step 2: Code Review

Before committing code, validate implementation:

1. Run all validation checklists above
2. Test with missing inputs (should raise clear errors)
3. Test with different newsletter types (should work universally)
4. Verify no hardcoded return structures

### Step 3: Integration Testing

Before deployment:

1. Test context propagation through full pipeline
2. Verify existing agents still work (backward compatibility)
3. Test error handling with real missing data scenarios
4. Validate LLM assessment quality across newsletter types

## Key Principle

**Trust LLM intelligence, orchestrate existing agents, propagate context explicitly, and fail fast with clear errors.**
