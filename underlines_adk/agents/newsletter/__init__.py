"""
Newsletter Generation Agents Package.

This package implements a sophisticated newsletter generation pipeline using
Google's Agent Development Kit (ADK). The pipeline follows an ultra-lean,
declarative architecture with three stages:

1. Story Identification - Discovers relevant stories via wide search
2. Parallel Deep Research - Conducts in-depth research on each story
3. Newsletter Generation - Crafts and polishes the final newsletter

Key Components:
    - story_identification: Wide search and story list validation agents
    - deep_research: Parallel research orchestration and validation
    - newsletter_generation: Newsletter writing and review agents
    - managing_editor: Top-level sequential orchestrator

The system uses ADK's workflow agents (SequentialAgent, LoopAgent, ParallelAgent)
and minimizes custom code, relying on declarative agent composition and the
final_answer mechanism for automatic loop termination.
"""

from .deep_research import (
    FanOutResearchAgent,
    create_deep_research_critic,
    create_deep_research_loop,
    create_deep_researcher_agent,
)
from .managing_editor import create_managing_editor
from .newsletter_generation import create_newsletter_loop
from .story_identification import create_json_repair_agent, create_wide_search_loop

__all__ = [
    # Story Identification
    "create_wide_search_loop",
    "create_json_repair_agent",
    # Deep Research
    "FanOutResearchAgent",
    "create_deep_researcher_agent",
    "create_deep_research_critic",
    "create_deep_research_loop",
    # Newsletter Generation
    "create_newsletter_loop",
    # Top-level Orchestrator
    "create_managing_editor",
]
