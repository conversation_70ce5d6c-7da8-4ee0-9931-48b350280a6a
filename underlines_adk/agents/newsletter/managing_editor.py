"""
Top-Level Newsletter Pipeline Orchestrator.

This module defines the ManagingEditor, a SequentialAgent that orchestrates
the entire newsletter generation pipeline through three declarative stages:

1. Story Identification (WideSearchLoop)
2. Parallel Deep Research (FanOutResearchAgent)
3. Newsletter Generation (NewsletterLoop)

The ManagingEditor represents the peak of declarative ADK design, with the
entire complex workflow defined as a simple sequence of sub-agents. State
flows through four keys: user_request → stories_array → story_bundle → newsletter.
"""

from typing import Optional

from google.adk.agents import SequentialAgent

from .deep_research import FanOutResearchAgent
from .newsletter_generation import create_newsletter_loop
from .story_identification import create_json_repair_agent, create_wide_search_loop


def create_managing_editor(
    use_json_repair: bool = False,
    wide_search_iterations: int = 3,
    research_iterations_per_story: int = 3,
    research_lookback_days: int = 7,
    newsletter_iterations: int = 5,
) -> SequentialAgent:
    """Create the complete newsletter generation pipeline.

    Args:
        use_json_repair: Whether to include JSON repair after story identification
        wide_search_iterations: Max iterations for story discovery
        research_iterations_per_story: Max iterations for each story research
        research_lookback_days: Days to look back for research
        newsletter_iterations: Max iterations for newsletter polish

    Returns:
        SequentialAgent that orchestrates the entire pipeline
    """
    # Create the sub-agents
    sub_agents = []

    # Stage 1: Story Identification
    wide_search_loop = create_wide_search_loop(max_iterations=wide_search_iterations)
    sub_agents.append(wide_search_loop)

    # Optional JSON repair
    if use_json_repair:
        json_repair_agent = create_json_repair_agent()
        sub_agents.append(json_repair_agent)

    # Stage 2: Parallel Deep Research
    fan_out_agent = FanOutResearchAgent(
        max_iterations_per_story=research_iterations_per_story,
        lookback_days=research_lookback_days,
    )
    sub_agents.append(fan_out_agent)

    # Stage 3: Newsletter Generation
    newsletter_loop = create_newsletter_loop(max_iterations=newsletter_iterations)
    sub_agents.append(newsletter_loop)

    # Create the managing editor
    return SequentialAgent(name="ManagingEditorPipeline", sub_agents=sub_agents)


# Export components
__all__ = ["create_managing_editor"]
