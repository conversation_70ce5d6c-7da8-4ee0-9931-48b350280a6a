# Newsletter Generation Agents

### Purpose
<!-- readme:purpose-start -->
AI-powered newsletter generation pipeline that transforms natural language requirements into comprehensive, well-researched newsletters using Google's Agent Development Kit.
Implements a three-stage architecture: story discovery, parallel deep research, and newsletter composition.
<!-- readme:purpose-end -->

### Architecture
<!-- readme:architecture-start -->
The pipeline follows an ultra-lean, declarative design with three sequential stages orchestrated by the ManagingEditor. Each stage uses ADK's workflow agents (LoopAgent, ParallelAgent) with the final_answer mechanism for automatic termination.

```mermaid
graph TD
    subgraph "Stage 1: Story Identification"
        A[WideSearchAgent] -->|discovers stories| B[StoryListCritic]
        B -->|loop until satisfied| A
        B -->|FINAL STORY ARRAY| C[stories_array]
    end
    
    subgraph "Stage 2: Parallel Deep Research"
        C --> D[FanOutResearchAgent]
        D -->|creates N loops| E[ParallelAgent]
        E --> E1[DeepResearchLoop_1]
        E --> E2[DeepResearchLoop_2]
        E --> EN[DeepResearchLoop_N]
        E1 & E2 & EN -->|aggregated| F[story_bundle]
    end
    
    subgraph "Stage 3: Newsletter Generation"
        F --> G[NewsletterWriterAgent]
        G -->|draft| H[NewsletterReviewerAgent]
        H -->|loop until polished| G
        H -->|FINAL NEWSLETTER| I[newsletter]
    end
    
    J[user_request] -.->|context| A & D & G
```

**State Flow:**
- `user_request` → Initial newsletter requirements
- `stories_array` → JSON array of discovered story topics
- `story_bundle` → Aggregated deep research for all stories
- `newsletter` → Final polished newsletter

**Key Design Principles:**
1. **Declarative Composition**: Entire pipeline defined as agent sequences
2. **Minimal Custom Code**: Only FanOutResearchAgent contains imperative logic
3. **Automatic Termination**: Critics use `auto_mark_final=True` 
4. **Parallel Efficiency**: Research conducted concurrently for all stories
<!-- readme:architecture-end -->

### Contents
<!-- readme:contents-start -->
```text
newsletter/
├── __init__.py                # Package exports and imports
├── story_identification.py    # Stage 1: Wide search and story validation
├── deep_research.py          # Stage 2: Parallel research orchestration
├── newsletter_generation.py  # Stage 3: Newsletter writing and review
├── managing_editor.py        # Top-level pipeline orchestrator
└── README.md                 # This documentation
```
<!-- readme:contents-end -->

### Test Refs
<!-- readme:test-refs-start -->
* tests/unit/test_story_identification.py
* tests/unit/test_deep_research.py
* tests/unit/test_newsletter_generation.py
* tests/integration/test_newsletter_pipeline.py
<!-- readme:test-refs-end -->

### Doc Refs
<!-- readme:doc-refs-start -->
* docs/prd.md - Product requirements for newsletter generation
* docs/tech-spec.md - Technical architecture and ADK patterns
* underlines_adk/tools/README.md - Exa search tools documentation
<!-- readme:doc-refs-end -->

### Dependent Refs
<!-- readme:dependent-refs-start -->
* examples/newsletter_demo.py - Example usage of ManagingEditor
* underlines_adk/tools/exa_tools.py - Search tools used by agents
<!-- readme:dependent-refs-end -->

### Examples
<!-- readme:examples-start -->
```python
# Basic usage with default configuration
from underlines_adk.agents.newsletter import ManagingEditor
from underlines_adk.sessions import LoggedSessionService
from google.adk.runners import Runner

# Create session with initial request
session_service = LoggedSessionService()
initial_state = {
    "user_request": "Create a weekly AI and climate tech newsletter"
}

# Run the pipeline
runner = Runner(
    agent=ManagingEditor,
    app_name="newsletter_generator",
    session_service=session_service
)

# Execute and get newsletter
result = await runner.arun(
    user_id="user_123",
    session_id="session_001",
    initial_state=initial_state
)
newsletter = result.state.get("newsletter")
```

```python
# Custom configuration for specific needs
from underlines_adk.agents.newsletter import create_managing_editor

# Create editor with custom parameters
custom_editor = create_managing_editor(
    use_json_repair=True,           # Ensure robust JSON handling
    wide_search_iterations=5,       # More thorough story discovery
    research_iterations_per_story=4, # Deeper research per story
    research_lookback_days=14,      # Two weeks of content
    newsletter_iterations=7         # More polish iterations
)

# Use with specific industry focus
biotech_state = {
    "user_request": """Weekly biotech newsletter focusing on:
    - TL1A antibody research and clinical trials
    - Gene therapy breakthroughs
    - FDA approvals and regulatory changes
    - Investment activity and M&A
    Include scientific depth with accessible explanations."""
}
```
<!-- readme:examples-end -->

### Todos
<!-- readme:todos-start -->
* Add configuration for different newsletter formats (executive brief, detailed analysis)
* Implement caching for repeated searches within time windows
* Add support for newsletter templates and custom styling
* Create monitoring for research quality metrics
<!-- readme:todos-end --> 